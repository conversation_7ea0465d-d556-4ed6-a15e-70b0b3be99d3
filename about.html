<!DOCTYPE html>
<html lang="fr">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<title>À propos - Power Fitness</title>
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
	<script src="https://cdn.tailwindcss.com"></script>
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
	<link rel="icon" type="image/png" href="imgs/LOGO POWER FITNESS.png" />
	<link rel="icon" type="image/png" sizes="32x32" href="imgs/LOGO POWER FITNESS.png" />
	<link rel="icon" type="image/png" sizes="16x16" href="imgs/LOGO POWER FITNESS.png" />
	<link rel="apple-touch-icon" href="imgs/LOGO POWER FITNESS.png" />
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						'punch-red': '#CB2129',
						'punch-white': '#FFFFFF',
						'punch-black': '#000000',
					},
					fontFamily: {
						'punch': ['Roboto', 'system-ui', '-apple-system', 'Segoe UI', 'Ubuntu', 'Cantarell', 'Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
					},
					animation: {
						'fade-in': 'fadeIn 0.6s ease-out',
						'slide-up': 'slideUp 0.8s ease-out',
					}
				}
			}
		}
	</script>
	<style>
		@keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes slideUp { from { opacity: 0; transform: translateY(40px); } to { opacity: 1; transform: translateY(0); } }

		body {
			background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
			background-attachment: fixed;
			position: relative;
			overflow-x: hidden;
			font-family: 'punch', system-ui, sans-serif;
		}

		body::before {
			content: '';
			position: fixed; inset: 0;
			background: radial-gradient(circle at 20% 20%, rgba(203, 33, 41, 0.15) 0%, transparent 50%),
			radial-gradient(circle at 80% 80%, rgba(203, 33, 41, 0.1) 0%, transparent 50%);
			pointer-events: none; z-index: 0;
		}

		.glass { backdrop-filter: blur(10px); background: rgba(255,255,255,0.12); border: 1px solid rgba(255,255,255,0.2); }
		.section-title { color: #ffffff; font-weight: 800; font-size: 1.875rem; line-height: 2.25rem; }
		@media (min-width: 768px) { .section-title { font-size: 2.25rem; line-height: 2.5rem; } }
		.section-subtitle { color: #d1d5db; }

		.reveal { opacity: 0; transform: translateY(20px); transition: opacity .6s ease, transform .6s ease; }
		.reveal.visible { opacity: 1; transform: translateY(0); }

		/* Barre de Réseaux Sociaux */
		.social-button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 60px;
			height: 60px;
			border-radius: 50%;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
			position: relative;
			overflow: hidden;
		}

		.whatsapp-button {
			background: #25D366;
		}

		.linkedin-button {
			background: #0077B5;
		}

		.facebook-button {
			background: #1877F2;
		}

		.tiktok-button {
			background: #000000;
		}

		.instagram-button {
			background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D, #F56040, #F77737, #FCAF45, #FFDC80);
		}

		.social-button:hover {
			transform: scale(1.1);
			box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
		}

		.social-button::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
			transition: left 0.5s;
		}

		.social-button:hover::before {
			left: 100%;
		}

		/* Animation d'apparition */
		.social-button {
			animation: slideInRight 0.6s ease-out forwards;
			opacity: 0;
			transform: translateX(100px);
		}

		.social-button:nth-child(1) { animation-delay: 0.1s; }
		.social-button:nth-child(2) { animation-delay: 0.2s; }
		.social-button:nth-child(3) { animation-delay: 0.3s; }
		.social-button:nth-child(4) { animation-delay: 0.4s; }
		.social-button:nth-child(5) { animation-delay: 0.5s; }

		@keyframes slideInRight {
			to {
				opacity: 1;
				transform: translateX(0);
			}
		}

		/* Responsive */
		@media (max-width: 768px) {
			/* Barre de réseaux sociaux responsive */
			.social-media-bar {
				right: 8px !important;
				padding-right: 0 !important;
				top: 50% !important;
				transform: translateY(-50%) !important;
			}

			.social-media-bar .flex {
				gap: 12px !important;
			}

			.social-button {
				width: 45px;
				height: 45px;
			}
			.social-button svg {
				width: 28px;
				height: 28px;
			}
		}

		@media (max-width: 480px) {
			/* Très petits écrans */
			.social-media-bar {
				right: 4px !important;
			}

			.social-media-bar .flex {
				gap: 8px !important;
			}

			.social-button {
				width: 40px;
				height: 40px;
			}
			.social-button svg {
				width: 24px;
				height: 24px;
			}
		}
	</style>
</head>
<body class="min-h-screen text-white" style="font-family: 'Roboto', system-ui, -apple-system, 'Segoe UI', Ubuntu, Cantarell, 'Noto Sans', 'Helvetica Neue', Arial, sans-serif;">
	<!-- Hero Background Wrapper -->
	<div class="min-h-screen" style="background-image: url('imgs/background.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat; background-attachment: fixed;">
		<!-- Navbar -->
		<nav class="bg-transparent px-6 py-4 absolute top-0 left-0 w-full z-20">
			<div class="max-w-7xl mx-auto flex items-center justify-between">
				<a href="index.html" class="flex items-center space-x-3">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" class="w-32 h-auto drop-shadow-[0_0_20px_rgba(203,33,41,0.3)]" />
				</a>
				<div class="hidden md:flex items-center space-x-6">
					<a href="index.html" class="hover:text-punch-red nav-link">Accueil</a>
					<a href="abonnements.html" class="hover:text-punch-red nav-link">Abonnements</a>
					<a href="coach.html" class="hover:text-punch-red nav-link">Coachs</a>
					<a href="planning.html" class="hover:text-punch-red nav-link">Planning</a>
					<a href="contact.html" class="hover:text-punch-red nav-link">Contact</a>
					<a href="register.php" class="px-4 py-2 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire</a>
				</div>
			</div>
		</nav>

		<!-- Hero -->
		<section class="max-w-5xl mx-auto px-6 pt-56 pb-24">
			<div class="flex flex-col items-center text-center gap-8">
				<div class="animate-fade-in">
					<h1 class="text-4xl md:text-6xl font-extrabold leading-tight">À propos de <span class="text-punch-red">Power Fitness</span></h1>
					<p class="mt-5 text-gray-300 text-lg max-w-3xl mx-auto">Découvrez notre histoire, notre mission et notre équipe passionnée qui vous accompagne dans votre transformation physique.</p>
				</div>
			</div>
		</section>
	</div>

	<!-- Notre Histoire -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="grid lg:grid-cols-2 gap-12 items-center">
				<div class="reveal">
					<h2 class="section-title">Notre Histoire</h2>
					<p class="section-subtitle mt-2">Une passion qui dure depuis 2017</p>
					<div class="mt-6 space-y-4 text-gray-300">
						<p>Power Fitness est né d'une vision simple : créer un espace où chacun peut transformer son corps et son esprit dans un environnement motivant et professionnel.</p>
						<p>Depuis notre ouverture, nous avons accompagné plus de 1200 membres dans leur parcours fitness, avec des résultats exceptionnels et une communauté toujours plus forte.</p>
						<p>Notre salle a évolué au fil des années pour devenir un centre de fitness moderne, équipé des dernières technologies et animé par une équipe de coachs passionnés et certifiés.</p>
					</div>
				</div>
				<div class="reveal">
					<div class="glass rounded-xl overflow-hidden border border-white/20">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.20 (1).jpeg" alt="Histoire Power Fitness" class="w-full h-auto">
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Notre Mission -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center reveal">
				<h2 class="section-title">Notre Mission</h2>
				<p class="section-subtitle mt-2">Vous aider à devenir la meilleure version de vous-même</p>
			</div>
			
			<div class="mt-12 grid md:grid-cols-3 gap-8">
				<div class="glass rounded-xl p-8 border border-white/20 text-center reveal">
					<div class="w-16 h-16 bg-punch-red rounded-full flex items-center justify-center mx-auto mb-4">
						<i class="fa-solid fa-heart text-2xl text-white"></i>
					</div>
					<h3 class="text-xl font-bold mb-3">Santé & Bien-être</h3>
					<p class="text-gray-300">Promouvoir un mode de vie sain et équilibré pour tous nos membres.</p>
				</div>
				
				<div class="glass rounded-xl p-8 border border-white/20 text-center reveal">
					<div class="w-16 h-16 bg-punch-red rounded-full flex items-center justify-center mx-auto mb-4">
						<i class="fa-solid fa-users text-2xl text-white"></i>
					</div>
					<h3 class="text-xl font-bold mb-3">Communauté</h3>
					<p class="text-gray-300">Créer une communauté motivante où chacun s'encourage mutuellement.</p>
				</div>
				
				<div class="glass rounded-xl p-8 border border-white/20 text-center reveal">
					<div class="w-16 h-16 bg-punch-red rounded-full flex items-center justify-center mx-auto mb-4">
						<i class="fa-solid fa-trophy text-2xl text-white"></i>
					</div>
					<h3 class="text-xl font-bold mb-3">Excellence</h3>
					<p class="text-gray-300">Maintenir les plus hauts standards de qualité et de professionnalisme.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Nos Valeurs -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="grid lg:grid-cols-2 gap-12 items-center">
				<div class="reveal">
					<div class="glass rounded-xl overflow-hidden border border-white/20">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.20 (2).jpeg" alt="Valeurs Power Fitness" class="w-full h-auto">
					</div>
				</div>
				<div class="reveal">
					<h2 class="section-title">Nos Valeurs</h2>
					<p class="section-subtitle mt-2">Les principes qui nous guident</p>
					
					<div class="mt-8 space-y-6">
						<div class="flex items-start space-x-4">
							<div class="w-8 h-8 bg-punch-red rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<i class="fa-solid fa-check text-white text-sm"></i>
							</div>
							<div>
								<h3 class="font-bold">Passion</h3>
								<p class="text-gray-300 text-sm">Nous sommes passionnés par le fitness et transmettons cette passion à nos membres.</p>
							</div>
						</div>
						
						<div class="flex items-start space-x-4">
							<div class="w-8 h-8 bg-punch-red rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<i class="fa-solid fa-check text-white text-sm"></i>
							</div>
							<div>
								<h3 class="font-bold">Professionnalisme</h3>
								<p class="text-gray-300 text-sm">Une équipe qualifiée et des services de qualité pour votre satisfaction.</p>
							</div>
						</div>
						
						<div class="flex items-start space-x-4">
							<div class="w-8 h-8 bg-punch-red rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<i class="fa-solid fa-check text-white text-sm"></i>
							</div>
							<div>
								<h3 class="font-bold">Innovation</h3>
								<p class="text-gray-300 text-sm">Nous restons à la pointe des dernières tendances et technologies fitness.</p>
							</div>
						</div>
						
						<div class="flex items-start space-x-4">
							<div class="w-8 h-8 bg-punch-red rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<i class="fa-solid fa-check text-white text-sm"></i>
							</div>
							<div>
								<h3 class="font-bold">Inclusion</h3>
								<p class="text-gray-300 text-sm">Un espace accueillant pour tous, quel que soit votre niveau ou vos objectifs.</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Notre Équipe -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center reveal">
				<h2 class="section-title">Notre Équipe</h2>
				<p class="section-subtitle mt-2">Des professionnels passionnés à votre service</p>
			</div>
			
			<div class="mt-12 grid md:grid-cols-3 gap-8">
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<div class="w-24 h-24 mx-auto rounded-full bg-white/10 flex items-center justify-center border border-white/20 mb-4">
						<i class="fa-solid fa-person-running text-3xl text-punch-red"></i>
					</div>
					<h3 class="text-xl font-bold">Yassine</h3>
					<p class="text-punch-red text-sm mb-2">Directeur & Coach Principal</p>
					<p class="text-gray-300 text-sm">10 ans d'expérience en fitness, spécialisé en musculation et cross training.</p>
				</div>
				
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<div class="w-24 h-24 mx-auto rounded-full bg-white/10 flex items-center justify-center border border-white/20 mb-4">
						<i class="fa-solid fa-hand-fist text-3xl text-punch-red"></i>
					</div>
					<h3 class="text-xl font-bold">Samir</h3>
					<p class="text-punch-red text-sm mb-2">Coach Boxe & Cardio</p>
					<p class="text-gray-300 text-sm">Ancien champion de boxe, passionné par l'enseignement et la motivation.</p>
				</div>
				
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<div class="w-24 h-24 mx-auto rounded-full bg-white/10 flex items-center justify-center border border-white/20 mb-4">
						<i class="fa-solid fa-spa text-3xl text-punch-red"></i>
					</div>
					<h3 class="text-xl font-bold">Imane</h3>
					<p class="text-punch-red text-sm mb-2">Coach Yoga & Mobilité</p>
					<p class="text-gray-300 text-sm">Instructrice certifiée en yoga et pilates, spécialisée dans la récupération.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Statistiques -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="grid grid-cols-2 md:grid-cols-4 gap-6">
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<div class="text-3xl font-extrabold text-punch-red" data-count="7">0</div>
					<div class="text-gray-300 text-sm mt-1">Années d'expérience</div>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<div class="text-3xl font-extrabold text-punch-red" data-count="1200">0</div>
					<div class="text-gray-300 text-sm mt-1">Membres satisfaits</div>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<div class="text-3xl font-extrabold text-punch-red" data-count="35">0</div>
					<div class="text-gray-300 text-sm mt-1">Cours par semaine</div>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<div class="text-3xl font-extrabold text-punch-red" data-count="12">0</div>
					<div class="text-gray-300 text-sm mt-1">Coachs certifiés</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Footer -->
	<footer class="py-10 border-t border-white/10">
		<div class="max-w-7xl mx-auto px-6 flex flex-col md:flex-row items-center justify-between gap-4">
			<div class="flex items-center space-x-3">
				<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" class="w-20 h-auto" />
				<span class="text-gray-300 text-sm">© 2024 Power Fitness. Tous droits réservés.</span>
			</div>
			<div class="flex items-center space-x-4 text-gray-300">
				<a href="#" class="hover:text-punch-red"><i class="fa-brands fa-facebook"></i></a>
				<a href="#" class="hover:text-punch-red"><i class="fa-brands fa-instagram"></i></a>
				<a href="#" class="hover:text-punch-red"><i class="fa-brands fa-tiktok"></i></a>
			</div>
		</div>
	</footer>



	<!-- Scripts -->
	<script>
		(function() {
			// Reveal on scroll
			const revealEls = Array.from(document.querySelectorAll('.reveal'));
			const observer = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						entry.target.classList.add('visible');
						observer.unobserve(entry.target);
					}
				});
			}, { threshold: 0.15 });
			revealEls.forEach(el => observer.observe(el));

			// Count-up animation
			function animateCount(el) {
				const target = parseInt(el.getAttribute('data-count') || '0', 10);
				let start = 0;
				const duration = 1200;
				const startTs = performance.now();
				function step(ts) {
					const progress = Math.min((ts - startTs) / duration, 1);
					const val = Math.floor(progress * target);
					el.textContent = val.toString();
					if (progress < 1) requestAnimationFrame(step);
				}
				requestAnimationFrame(step);
			}
			const counters = document.querySelectorAll('[data-count]');
			const counterObs = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						animateCount(entry.target);
						counterObs.unobserve(entry.target);
					}
				});
			}, { threshold: 0.5 });
			counters.forEach(el => counterObs.observe(el));
		})();
	</script>
</body>
</html>
