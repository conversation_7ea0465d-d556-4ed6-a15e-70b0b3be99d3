<!DOCTYPE html>
<html lang="fr">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<title>Abonnements - Power Fitness</title>
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
	<script src="https://cdn.tailwindcss.com"></script>
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
	<link rel="icon" type="image/png" href="imgs/LOGO POWER FITNESS.png" />
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						'punch-red': '#CB2129',
						'punch-white': '#FFFFFF',
						'punch-black': '#000000',
					},
					fontFamily: {
						'punch': ['Roboto', 'system-ui', '-apple-system', 'Segoe UI', 'Ubuntu', 'Cantarell', 'Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
					},
					animation: {
						'fade-in': 'fadeIn 0.6s ease-out',
						'slide-up': 'slideUp 0.8s ease-out',
						'bounce-slow': 'bounce 2s infinite',
						'pulse-slow': 'pulse 3s infinite',
					}
				}
			}
		}
	</script>
	<style>
		@keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes slideUp { from { opacity: 0; transform: translateY(40px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes floatSlow { 0% { transform: translateY(0px); } 50% { transform: translateY(-10px); } 100% { transform: translateY(0px); } }
		@keyframes glow { 0% { box-shadow: 0 0 20px rgba(203, 33, 41, 0.3); } 50% { box-shadow: 0 0 40px rgba(203, 33, 41, 0.6); } 100% { box-shadow: 0 0 20px rgba(203, 33, 41, 0.3); } }
		@keyframes shimmer { 0% { background-position: -200% 0; } 100% { background-position: 200% 0; } }
		@keyframes sparkle { 0%, 100% { opacity: 0; transform: scale(0); } 50% { opacity: 1; transform: scale(1); } }
		@keyframes cardFloat { 0%, 100% { transform: translateY(0px) rotateY(0deg); } 50% { transform: translateY(-15px) rotateY(5deg); } }
		@keyframes priceGlow { 0% { text-shadow: 0 0 10px rgba(203, 33, 41, 0.5); } 50% { text-shadow: 0 0 30px rgba(203, 33, 41, 1), 0 0 40px rgba(203, 33, 41, 0.8); } 100% { text-shadow: 0 0 10px rgba(203, 33, 41, 0.5); } }
		@keyframes borderPulse { 0% { border-color: rgba(203, 33, 41, 0.3); } 50% { border-color: rgba(203, 33, 41, 1); } 100% { border-color: rgba(203, 33, 41, 0.3); } }

		body {
			background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
			background-attachment: fixed;
			position: relative;
			overflow-x: hidden;
			font-family: 'punch', system-ui, sans-serif;
		}

		body::before {
			content: '';
			position: fixed; inset: 0;
			background: radial-gradient(circle at 20% 20%, rgba(203, 33, 41, 0.15) 0%, transparent 50%),
			radial-gradient(circle at 80% 80%, rgba(203, 33, 41, 0.1) 0%, transparent 50%);
			pointer-events: none; z-index: 0;
		}

		.glass { backdrop-filter: blur(10px); background: rgba(255,255,255,0.12); border: 1px solid rgba(255,255,255,0.2); }
		.section-title { color: #ffffff; font-weight: 800; font-size: 1.875rem; line-height: 2.25rem; }
		@media (min-width: 768px) { .section-title { font-size: 2.25rem; line-height: 2.5rem; } }
		.section-subtitle { color: #d1d5db; }

		.reveal { opacity: 0; transform: translateY(20px); transition: opacity .6s ease, transform .6s ease; }
		.reveal.visible { opacity: 1; transform: translateY(0); }

		.pricing-card {
			transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
			position: relative;
			overflow: hidden;
			perspective: 1000px;
		}
		.pricing-card::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
			transition: left 0.5s;
		}
		.pricing-card:hover::before {
			left: 100%;
		}
		.pricing-card:hover {
			transform: translateY(-20px) scale(1.05) rotateY(5deg);
			animation: glow 2s infinite, cardFloat 3s ease-in-out infinite;
			box-shadow: 0 25px 50px rgba(203, 33, 41, 0.3), 0 0 0 1px rgba(203, 33, 41, 0.1);
		}
		.pricing-card.popular {
			animation: borderPulse 2s infinite;
		}
		.pricing-card.popular:hover {
			animation: glow 2s infinite, cardFloat 3s ease-in-out infinite, borderPulse 2s infinite;
		}
		.popular-badge {
			background: linear-gradient(45deg, #CB2129, #ff4757, #CB2129);
			background-size: 200% 200%;
			animation: shimmer 2s infinite;
		}
		.price-number {
			animation: priceGlow 3s ease-in-out infinite;
		}
		.sparkle {
			position: absolute;
			width: 4px;
			height: 4px;
			background: #fff;
			border-radius: 50%;
			animation: sparkle 2s infinite;
		}
		.sparkle:nth-child(1) { top: 20%; left: 20%; animation-delay: 0s; }
		.sparkle:nth-child(2) { top: 80%; left: 80%; animation-delay: 0.5s; }
		.sparkle:nth-child(3) { top: 50%; left: 10%; animation-delay: 1s; }
		.sparkle:nth-child(4) { top: 30%; left: 90%; animation-delay: 1.5s; }

		.feature-item {
			transition: all 0.3s ease;
			position: relative;
		}
		.feature-item:hover {
			transform: translateX(10px);
			color: #CB2129;
		}
		.feature-item::before {
			content: '';
			position: absolute;
			left: -10px;
			top: 50%;
			transform: translateY(-50%);
			width: 0;
			height: 2px;
			background: linear-gradient(90deg, #CB2129, #ff4757);
			transition: width 0.3s ease;
		}
		.feature-item:hover::before {
			width: 5px;
		}

		/* Barre de Réseaux Sociaux */
		.social-button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 45px;
			height: 45px;
			border-radius: 50%;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
			position: relative;
			overflow: hidden;
		}

		.whatsapp-button {
			background: #25D366;
		}

		.linkedin-button {
			background: #0077B5;
		}

		.facebook-button {
			background: #1877F2;
		}

		.tiktok-button {
			background: #000000;
		}

		.instagram-button {
			background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D, #F56040, #F77737, #FCAF45, #FFDC80);
		}

		.social-button:hover {
			transform: scale(1.1);
			box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
		}

		.social-button::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
			transition: left 0.5s;
		}

		.social-button:hover::before {
			left: 100%;
		}

		/* Animation d'apparition */
		.social-button {
			animation: slideInRight 0.6s ease-out forwards;
			opacity: 0;
			transform: translateX(100px);
		}

		.social-button:nth-child(1) { animation-delay: 0.1s; }
		.social-button:nth-child(2) { animation-delay: 0.2s; }
		.social-button:nth-child(3) { animation-delay: 0.3s; }
		.social-button:nth-child(4) { animation-delay: 0.4s; }
		.social-button:nth-child(5) { animation-delay: 0.5s; }

		@keyframes slideInRight {
			to {
				opacity: 1;
				transform: translateX(0);
			}
		}

		/* Menu Mobile - Sidebar Style */
		.mobile-menu {
			position: fixed;
			top: 0;
			left: 0;
			width: 220px;
			height: 100vh;
			background: rgba(0, 0, 0, 0.95);
			backdrop-filter: blur(15px);
			z-index: 50;
			transform: translateX(-100%);
			transition: transform 0.3s ease-in-out;
			border-right: 1px solid rgba(203, 33, 41, 0.3);
		}

		.mobile-menu.active {
			transform: translateX(0);
		}

		/* Overlay pour fermer le menu */
		.mobile-menu-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100vh;
			background: rgba(0, 0, 0, 0.5);
			z-index: 49;
			opacity: 0;
			visibility: hidden;
			transition: all 0.3s ease-in-out;
		}

		.mobile-menu-overlay.active {
			opacity: 1;
			visibility: visible;
		}

		.mobile-menu-content {
			display: flex;
			flex-direction: column;
			padding: 2rem 0;
			height: 100%;
		}

		.mobile-menu-header {
			padding: 0 1.5rem 1.5rem;
			border-bottom: 1px solid rgba(255, 255, 255, 0.1);
			margin-bottom: 1rem;
		}

		.mobile-menu-header img {
			width: 120px;
			height: auto;
		}

		.mobile-menu a {
			color: white;
			font-size: 1rem;
			font-weight: 500;
			text-decoration: none;
			padding: 0.875rem 1.5rem;
			transition: all 0.3s ease;
			border-left: 3px solid transparent;
			display: flex;
			align-items: center;
		}

		.mobile-menu a:hover,
		.mobile-menu a.text-punch-red {
			background: rgba(203, 33, 41, 0.1);
			border-left-color: #cb2129;
			color: #cb2129;
		}

		.mobile-menu a.bg-punch-red {
			background: #cb2129;
			color: white;
			margin: 1rem 1.5rem;
			border-radius: 0.5rem;
			border-left: none;
			justify-content: center;
			font-weight: 600;
		}

		.mobile-menu a:nth-child(1) { transition-delay: 0.1s; }
		.mobile-menu a:nth-child(2) { transition-delay: 0.2s; }
		.mobile-menu a:nth-child(3) { transition-delay: 0.3s; }
		.mobile-menu a:nth-child(4) { transition-delay: 0.4s; }
		.mobile-menu a:nth-child(5) { transition-delay: 0.5s; }
		.mobile-menu a:nth-child(6) { transition-delay: 0.6s; }

		.mobile-menu a:hover {
			color: #cb2129;
			background: rgba(203, 33, 41, 0.1);
		}

		.hamburger {
			display: none;
			flex-direction: column;
			cursor: pointer;
			padding: 0.5rem;
		}

		.hamburger span {
			width: 25px;
			height: 3px;
			background: white;
			margin: 3px 0;
			transition: 0.3s;
			border-radius: 2px;
		}

		.hamburger.active span:nth-child(1) {
			transform: rotate(-45deg) translate(-5px, 6px);
		}

		.hamburger.active span:nth-child(2) {
			opacity: 0;
		}

		.hamburger.active span:nth-child(3) {
			transform: rotate(45deg) translate(-5px, -6px);
		}

		/* Responsive */
		@media (max-width: 768px) {
			/* Menu hamburger visible sur mobile */
			.hamburger {
				display: flex;
			}

			/* Logo plus petit sur mobile */
			nav img {
				width: 100px !important;
			}

			/* Barre de réseaux sociaux responsive */
			.social-media-bar {
				right: 8px !important;
				padding-right: 0 !important;
				top: 50% !important;
				transform: translateY(-50%) !important;
			}

			.social-media-bar .flex {
				gap: 12px !important;
			}

			.social-button {
				width: 38px;
				height: 38px;
			}
			.social-button svg {
				width: 24px;
				height: 24px;
			}
		}

		@media (max-width: 480px) {
			/* Logo encore plus petit sur très petits écrans */
			nav img {
				width: 80px !important;
			}

			/* Très petits écrans */
			.social-media-bar {
				right: 4px !important;
			}

			.social-media-bar .flex {
				gap: 8px !important;
			}

			.social-button {
				width: 32px;
				height: 32px;
			}
			.social-button svg {
				width: 20px;
				height: 20px;
			}
		}
	</style>
</head>
<body class="min-h-screen text-white">
	<!-- Hero Background -->
	<div class="min-h-screen" style="background-image: url('imgs/background.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat; background-attachment: fixed;">
		<!-- Navbar -->
		<nav class="bg-transparent px-6 py-4 absolute top-0 left-0 w-full z-20">
			<div class="max-w-7xl mx-auto flex items-center justify-between">
				<a href="index.html" class="flex items-center space-x-3">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" class="w-32 h-auto drop-shadow-[0_0_20px_rgba(203,33,41,0.3)]" />
				</a>

				<!-- Menu Desktop -->
				<div class="hidden md:flex items-center space-x-6">
					<a href="index.html" class="hover:text-punch-red nav-link">Accueil</a>
					<a href="abonnements.html" class="text-punch-red nav-link">Abonnements</a>
					<a href="coach.html" class="hover:text-punch-red nav-link">Coachs</a>
					<a href="planning.html" class="hover:text-punch-red nav-link">Planning</a>
					<a href="contact.html" class="hover:text-punch-red nav-link">Contact</a>
					<a href="register.php" class="px-4 py-2 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire</a>
				</div>

				<!-- Hamburger Menu Button -->
				<div class="hamburger md:hidden" id="hamburger">
					<span></span>
					<span></span>
					<span></span>
				</div>
			</div>
		</nav>

		<!-- Menu Mobile Overlay -->
		<div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

		<!-- Menu Mobile -->
		<div class="mobile-menu" id="mobileMenu">
			<div class="mobile-menu-content">
				<div class="mobile-menu-header">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" />
				</div>
				<a href="index.html" class="mobile-nav-link">Accueil</a>
				<a href="abonnements.html" class="mobile-nav-link text-punch-red">Abonnements</a>
				<a href="coach.html" class="mobile-nav-link">Coachs</a>
				<a href="planning.html" class="mobile-nav-link">Planning</a>
				<a href="contact.html" class="mobile-nav-link">Contact</a>
				<a href="register.php" class="mobile-nav-link bg-punch-red">S'inscrire</a>
			</div>
		</div>

		<!-- Hero Section -->
		<section class="max-w-5xl mx-auto px-6 pt-56 pb-24">
			<div class="flex flex-col items-center text-center gap-8">
				<div class="animate-fade-in">
					<h1 class="text-4xl md:text-6xl font-extrabold leading-tight">Choisissez votre <span class="text-punch-red">Abonnement</span></h1>
					<p class="mt-5 text-gray-300 text-lg max-w-3xl mx-auto">Des formules flexibles adaptées à vos besoins et votre budget. Commencez votre transformation dès aujourd'hui.</p>
				</div>
			</div>
		</section>
	</div>

	<!-- Pricing Plans -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Nos Formules d'Abonnement</h2>
				<p class="section-subtitle mt-4">Trouvez la formule parfaite pour atteindre vos objectifs fitness</p>
			</div>
			
			<div class="grid md:grid-cols-3 gap-8">
				<!-- Plan Mensuel -->
				<div class="pricing-card glass rounded-2xl p-8 border border-white/20 reveal relative">
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="text-center">
						<div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-punch-red to-red-700 flex items-center justify-center shadow-lg">
							<i class="fa-solid fa-rocket text-2xl text-white animate-bounce"></i>
						</div>
						<h3 class="text-2xl font-bold mb-2">Mensuel</h3>
						<div class="price-number text-5xl font-extrabold text-punch-red mb-6">199<span class="text-lg text-gray-300">DH/mois</span></div>
						<p class="text-gray-300 mb-8">Parfait pour commencer votre parcours fitness</p>
					</div>
					<ul class="space-y-4 mb-8">
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Accès illimité à la salle</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>1 cours collectif/semaine</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Programme d'initiation</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Vestiaires & douches</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Support client 7j/7</li>
					</ul>
					<a href="register.php" class="w-full block text-center px-6 py-3 bg-gradient-to-r from-white/10 to-white/20 border border-white/20 rounded-lg font-semibold hover:from-punch-red hover:to-red-700 hover:border-punch-red transition-all duration-300 transform hover:scale-105">Choisir ce plan</a>
				</div>

				<!-- Plan Trimestriel (Popular) -->
				<div class="pricing-card popular glass rounded-2xl p-8 border-2 border-punch-red reveal relative overflow-hidden">
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="popular-badge absolute top-0 right-0 px-4 py-2 text-white text-sm font-bold rounded-bl-lg flex items-center">
						<i class="fa-solid fa-crown mr-2 animate-wiggle"></i>POPULAIRE
					</div>
					<div class="text-center">
						<div class="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-br from-yellow-400 via-punch-red to-red-700 flex items-center justify-center shadow-2xl animate-pulse">
							<i class="fa-solid fa-star text-3xl text-white animate-spin"></i>
						</div>
						<h3 class="text-2xl font-bold mb-2">Trimestriel</h3>
						<div class="price-number text-5xl font-extrabold text-punch-red mb-6">549<span class="text-lg text-gray-300">DH/3mois</span></div>
						<p class="text-gray-300 mb-8">Le meilleur rapport qualité-prix</p>
					</div>
					<ul class="space-y-4 mb-8">
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Accès illimité à la salle</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>2 cours collectifs/semaine</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Bilan forme + suivi</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Coaching personnalisé</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Nutrition de base</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Accès zone VIP</li>
					</ul>
					<a href="register.php" class="w-full block text-center px-6 py-3 bg-gradient-to-r from-punch-red to-red-700 rounded-lg font-semibold hover:from-red-700 hover:to-punch-red transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-2xl">
						<i class="fa-solid fa-bolt mr-2 animate-bounce"></i>Choisir ce plan
					</a>
				</div>

				<!-- Plan Annuel -->
				<div class="pricing-card glass rounded-2xl p-8 border border-white/20 reveal relative">
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="sparkle"></div>
					<div class="text-center">
						<div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-purple-500 via-punch-red to-orange-500 flex items-center justify-center shadow-lg">
							<i class="fa-solid fa-trophy text-2xl text-white animate-bounce"></i>
						</div>
						<h3 class="text-2xl font-bold mb-2">Annuel</h3>
						<div class="price-number text-5xl font-extrabold text-punch-red mb-6">1999<span class="text-lg text-gray-300">DH/an</span></div>
						<p class="text-gray-300 mb-8">L'engagement ultime pour votre transformation</p>
					</div>
					<ul class="space-y-4 mb-8">
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Accès illimité à la salle</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Cours collectifs illimités</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Coaching personnalisé</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Plan nutritionnel complet</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Accès prioritaire équipements</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Invitations événements</li>
						<li class="feature-item flex items-center"><i class="fa-solid fa-check text-punch-red mr-3 animate-pulse"></i>Réductions partenaires</li>
					</ul>
					<a href="register.php" class="w-full block text-center px-6 py-3 bg-gradient-to-r from-white/10 to-white/20 border border-white/20 rounded-lg font-semibold hover:from-punch-red hover:to-red-700 hover:border-punch-red transition-all duration-300 transform hover:scale-105">
						<i class="fa-solid fa-gem mr-2 animate-pulse"></i>Choisir ce plan
					</a>
				</div>
			</div>
		</div>
	</section>

	<!-- Features Comparison -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Comparaison des Formules</h2>
				<p class="section-subtitle mt-4">Découvrez en détail ce qui est inclus dans chaque abonnement</p>
			</div>
			
			<div class="glass rounded-2xl p-8 border border-white/20 overflow-x-auto reveal">
				<table class="w-full text-left">
					<thead>
						<tr class="border-b border-white/20">
							<th class="py-4 text-gray-300">Fonctionnalités</th>
							<th class="py-4 text-center">Mensuel</th>
							<th class="py-4 text-center">Trimestriel</th>
							<th class="py-4 text-center">Annuel</th>
						</tr>
					</thead>
					<tbody class="text-sm">
						<tr class="border-b border-white/10">
							<td class="py-4">Accès salle de sport</td>
							<td class="py-4 text-center"><i class="fa-solid fa-check text-punch-red"></i></td>
							<td class="py-4 text-center"><i class="fa-solid fa-check text-punch-red"></i></td>
							<td class="py-4 text-center"><i class="fa-solid fa-check text-punch-red"></i></td>
						</tr>
						<tr class="border-b border-white/10">
							<td class="py-4">Cours collectifs</td>
							<td class="py-4 text-center">1/semaine</td>
							<td class="py-4 text-center">2/semaine</td>
							<td class="py-4 text-center">Illimité</td>
						</tr>
						<tr class="border-b border-white/10">
							<td class="py-4">Coaching personnalisé</td>
							<td class="py-4 text-center"><i class="fa-solid fa-xmark text-gray-500"></i></td>
							<td class="py-4 text-center"><i class="fa-solid fa-check text-punch-red"></i></td>
							<td class="py-4 text-center"><i class="fa-solid fa-check text-punch-red"></i></td>
						</tr>
						<tr class="border-b border-white/10">
							<td class="py-4">Plan nutritionnel</td>
							<td class="py-4 text-center"><i class="fa-solid fa-xmark text-gray-500"></i></td>
							<td class="py-4 text-center">Basique</td>
							<td class="py-4 text-center">Complet</td>
						</tr>
						<tr class="border-b border-white/10">
							<td class="py-4">Accès zone VIP</td>
							<td class="py-4 text-center"><i class="fa-solid fa-xmark text-gray-500"></i></td>
							<td class="py-4 text-center"><i class="fa-solid fa-check text-punch-red"></i></td>
							<td class="py-4 text-center"><i class="fa-solid fa-check text-punch-red"></i></td>
						</tr>
						<tr>
							<td class="py-4">Invitations événements</td>
							<td class="py-4 text-center"><i class="fa-solid fa-xmark text-gray-500"></i></td>
							<td class="py-4 text-center"><i class="fa-solid fa-xmark text-gray-500"></i></td>
							<td class="py-4 text-center"><i class="fa-solid fa-check text-punch-red"></i></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</section>

	<!-- Special Offers -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Offres Spéciales</h2>
				<p class="section-subtitle mt-4">Profitez de nos promotions limitées</p>
			</div>

			<div class="grid md:grid-cols-2 gap-8">
				<div class="glass rounded-2xl p-8 border border-punch-red reveal" style="animation: floatSlow 6s ease-in-out infinite;">
					<div class="flex items-center mb-6">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (6).jpeg" alt="Offre Étudiante" class="w-20 h-20 rounded-full object-cover mr-4">
						<div>
							<h3 class="text-xl font-bold">Offre Étudiante</h3>
							<p class="text-punch-red font-semibold">-20% sur tous les abonnements</p>
						</div>
					</div>
					<p class="text-gray-300 mb-4">Présente ta carte étudiante et bénéficie d'une réduction exceptionnelle sur tous nos abonnements.</p>
					<ul class="text-sm text-gray-200 space-y-2">
						<li>• Valable sur présentation de la carte étudiante</li>
						<li>• Non cumulable avec d'autres offres</li>
						<li>• Valide jusqu'au 31 décembre</li>
					</ul>
				</div>

				<div class="glass rounded-2xl p-8 border border-punch-red reveal" style="animation: floatSlow 6s 1s ease-in-out infinite;">
					<div class="flex items-center mb-6">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (7).jpeg" alt="Parrainage" class="w-20 h-20 rounded-full object-cover mr-4">
						<div>
							<h3 class="text-xl font-bold">Programme Parrainage</h3>
							<p class="text-punch-red font-semibold">1 mois offert</p>
						</div>
					</div>
					<p class="text-gray-300 mb-4">Parraine un ami et recevez tous les deux un mois d'abonnement gratuit.</p>
					<ul class="text-sm text-gray-200 space-y-2">
						<li>• Ton ami doit s'inscrire pour 3 mois minimum</li>
						<li>• Bonus applicable après 1 mois d'adhésion</li>
						<li>• Parrainage illimité</li>
					</ul>
				</div>
			</div>
		</div>
	</section>

	<!-- FAQ Section -->
	<section class="py-20">
		<div class="max-w-4xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Questions Fréquentes</h2>
				<p class="section-subtitle mt-4">Tout ce que vous devez savoir sur nos abonnements</p>
			</div>

			<div class="space-y-6">
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<h3 class="font-bold text-lg mb-3">Puis-je changer d'abonnement en cours ?</h3>
					<p class="text-gray-300">Oui, vous pouvez upgrader votre abonnement à tout moment. La différence sera calculée au prorata.</p>
				</div>

				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<h3 class="font-bold text-lg mb-3">Y a-t-il des frais d'inscription ?</h3>
					<p class="text-gray-300">Non, aucun frais d'inscription. Le prix affiché est le prix final que vous payez.</p>
				</div>

				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<h3 class="font-bold text-lg mb-3">Puis-je suspendre mon abonnement ?</h3>
					<p class="text-gray-300">Oui, vous pouvez suspendre votre abonnement pour des raisons médicales ou de voyage (justificatifs requis).</p>
				</div>

				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<h3 class="font-bold text-lg mb-3">Quels sont les moyens de paiement acceptés ?</h3>
					<p class="text-gray-300">Nous acceptons les cartes bancaires, virements, espèces et paiements mobiles (Orange Money, inwi money).</p>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="py-20">
		<div class="max-w-5xl mx-auto px-6 text-center glass rounded-2xl p-10 border border-white/20 reveal">
			<h2 class="text-3xl md:text-4xl font-extrabold mb-4">Prêt à Commencer ?</h2>
			<p class="text-gray-300 mb-8">Rejoignez plus de 1200 membres qui ont déjà transformé leur vie avec Power Fitness</p>
			<div class="flex flex-wrap justify-center gap-4">
				<a href="register.php" class="px-8 py-4 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire maintenant</a>
				<a href="index.html#contact" class="px-8 py-4 glass border border-white/20 rounded-lg font-semibold hover:bg-white/20 transition">Nous contacter</a>
			</div>
		</div>
	</section>

	<!-- Footer -->
	<footer class="bg-brand-black py-12 px-6">
		<div class="container mx-auto max-w-6xl">
			<div class="grid md:grid-cols-4 gap-8 mb-8">
				<div class="md:col-span-1">
					<div class="flex items-center mb-4">
						<span class="text-2xl font-bold text-white">POWER</span>
						<span class="text-2xl font-bold text-brand-red">FITNESS</span>
					</div>
					<p class="text-gray-400 mb-4">Transform your limits. Unleash your power.</p>
					<div class="flex space-x-4">
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
							</svg>
						</a>
					</div>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Programs</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Strength Training</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Cardio Power</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Functional Fitness</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">HIIT Sessions</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Facility</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Equipment</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Amenities</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Hours</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Location</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Contact</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Join Now</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Book Tour</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Support</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Careers</a></li>
					</ul>
				</div>
			</div>

			<div class="border-t border-gray-800 pt-8 text-center">
				<p class="text-gray-400">&copy; 2025 Power Fitness. All rights reserved.</p>
			</div>
		</div>
	</footer>



	<!-- Scripts -->
	<script>
		// Menu Mobile Toggle
		const hamburger = document.getElementById('hamburger');
		const mobileMenu = document.getElementById('mobileMenu');
		const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
		const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

		function openMobileMenu() {
			hamburger.classList.add('active');
			mobileMenu.classList.add('active');
			mobileMenuOverlay.classList.add('active');
			document.body.style.overflow = 'hidden';
		}

		function closeMobileMenu() {
			hamburger.classList.remove('active');
			mobileMenu.classList.remove('active');
			mobileMenuOverlay.classList.remove('active');
			document.body.style.overflow = '';
		}

		hamburger.addEventListener('click', function() {
			if (mobileMenu.classList.contains('active')) {
				closeMobileMenu();
			} else {
				openMobileMenu();
			}
		});

		// Fermer le menu mobile quand on clique sur l'overlay
		mobileMenuOverlay.addEventListener('click', closeMobileMenu);

		// Fermer le menu mobile quand on clique sur un lien
		mobileNavLinks.forEach(link => {
			link.addEventListener('click', closeMobileMenu);
		});

		// Fermer le menu mobile avec la touche Escape
		document.addEventListener('keydown', function(e) {
			if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
				closeMobileMenu();
			}
		});

		// Reveal on scroll
		const revealEls = Array.from(document.querySelectorAll('.reveal'));
		const observer = new IntersectionObserver((entries) => {
			entries.forEach(entry => {
				if (entry.isIntersecting) {
					entry.target.classList.add('visible');
					observer.unobserve(entry.target);
				}
			});
		}, { threshold: 0.15 });
		revealEls.forEach(el => observer.observe(el));

		// Smooth scroll for nav links
		document.querySelectorAll('a[href^="#"]').forEach(anchor => {
			anchor.addEventListener('click', function (e) {
				const href = this.getAttribute('href');
				if (!href || href === '#') return;
				e.preventDefault();
				document.querySelector(href)?.scrollIntoView({ behavior: 'smooth', block: 'start' });
			});
		});
	</script>

	<!-- Barre de Réseaux Sociaux Flottante -->
	 <div class="social-media-bar fixed right-4 top-1/2 transform -translate-y-1/2 z-50" style="padding-right: 10px;">
		<div class="flex flex-col space-y-5">
			<!-- WhatsApp -->
			<a href="https://api.whatsapp.com/send?phone=+212709702820" target="_blank" class="whatsapp-button social-button" onclick="this.href=window.innerWidth > 768 ? 'https://web.whatsapp.com/send?phone=+212709702820' : 'https://api.whatsapp.com/send?phone=+212709702820';">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.890-5.335 11.893-11.893A11.821 11.821 0 0020.525 3.687"></path>
				</svg>
			</a>

			<!-- LinkedIn -->
			<a class="linkedin-button social-button" href="https://www.linkedin.com/company/powerfitness/" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path>
				</svg>
			</a>

			<!-- Facebook -->
			<a class="facebook-button social-button" href="https://www.facebook.com/powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"></path>
				</svg>
			</a>

			<!-- TikTok -->
			<a class="tiktok-button social-button" href="https://www.tiktok.com/@powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"></path>
				</svg>
			</a>

			<!-- Instagram -->
			<a class="instagram-button social-button" href="https://www.instagram.com/powerfitness/" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path>
				</svg>
			</a>
		</div>
	</div>

</body>
</html>
