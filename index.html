
<!DOCTYPE html>
<html lang="fr">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<title>Power Fitness - Club de sport</title>
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
	<script src="https://cdn.tailwindcss.com"></script>
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
	<link rel="icon" type="image/png" href="imgs/LOGO POWER FITNESS.png" />
	<link rel="icon" type="image/png" sizes="32x32" href="imgs/LOGO POWER FITNESS.png" />
	<link rel="icon" type="image/png" sizes="16x16" href="imgs/LOGO POWER FITNESS.png" />
	<link rel="apple-touch-icon" href="imgs/LOGO POWER FITNESS.png" />
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						'punch-red': '#CB2129',
						'punch-white': '#FFFFFF',
						'punch-black': '#000000',
					},
					fontFamily: {
						'punch': ['Roboto', 'system-ui', '-apple-system', 'Segoe UI', 'Ubuntu', 'Cantarell', 'Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
					},
					animation: {
						'fade-in': 'fadeIn 0.6s ease-out',
						'slide-up': 'slideUp 0.8s ease-out',
					}
				}
			}
		}
	</script>
	<style>
		@keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes slideUp { from { opacity: 0; transform: translateY(40px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes floatSlow { 0% { transform: translateY(0px); } 50% { transform: translateY(-10px); } 100% { transform: translateY(0px); } }
		@keyframes marqueeScroll { 0% { transform: translateX(0); } 100% { transform: translateX(-50%); } }

		body {
			background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
			background-attachment: fixed;
			position: relative;
			overflow-x: hidden;
			font-family: 'punch', system-ui, sans-serif;
		}

		body::before {
			content: '';
			position: fixed; inset: 0;
			background: radial-gradient(circle at 20% 20%, rgba(203, 33, 41, 0.15) 0%, transparent 50%),
			radial-gradient(circle at 80% 80%, rgba(203, 33, 41, 0.1) 0%, transparent 50%);
			pointer-events: none; z-index: 0;
		}

		/***** Utilities *****/
		.glass { backdrop-filter: blur(10px); background: rgba(255,255,255,0.12); border: 1px solid rgba(255,255,255,0.2); }
		.section-title { color: #ffffff; font-weight: 800; font-size: 1.875rem; line-height: 2.25rem; }
		@media (min-width: 768px) { .section-title { font-size: 2.25rem; line-height: 2.5rem; } }
		.section-subtitle { color: #d1d5db; }

		/* Reveal on scroll */
		.reveal { opacity: 0; transform: translateY(20px); transition: opacity .6s ease, transform .6s ease; }
		.reveal.visible { opacity: 1; transform: translateY(0); }

		/* Marquee */
		.marquee { overflow: hidden; white-space: nowrap; }
		.marquee-track { display: inline-flex; gap: 3rem; padding: .5rem 0; animation: marqueeScroll 20s linear infinite; }

		/* Barre de Réseaux Sociaux */
		.social-button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 45px;
			height: 45px;
			border-radius: 50%;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
			position: relative;
			overflow: hidden;
		}

		.whatsapp-button {
			background: #CB2129;
		}

		.waze-button {
			background: #000000;
		}

		.facebook-button {
			background: #CB2129;
		}

		.tiktok-button {
			background: #000000;
		}

		.instagram-button {
			background: #CB2129;
		}

		.social-button:hover {
			transform: scale(1.1);
			box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
		}

		.social-button::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
			transition: left 0.5s;
		}

		.social-button:hover::before {
			left: 100%;
		}

		/* Animation d'apparition */
		.social-button {
			animation: slideInRight 0.6s ease-out forwards;
			opacity: 0;
			transform: translateX(100px);
		}

		.social-button:nth-child(1) { animation-delay: 0.1s; }
		.social-button:nth-child(2) { animation-delay: 0.2s; }
		.social-button:nth-child(3) { animation-delay: 0.3s; }
		.social-button:nth-child(4) { animation-delay: 0.4s; }
		.social-button:nth-child(5) { animation-delay: 0.5s; }

		@keyframes slideInRight {
			to {
				opacity: 1;
				transform: translateX(0);
			}
		}

		/* Menu Mobile - Sidebar Style */
		.mobile-menu {
			position: fixed;
			top: 0;
			left: 0;
			width: 220px;
			height: 100vh;
			background: rgba(0, 0, 0, 0.95);
			backdrop-filter: blur(15px);
			z-index: 50;
			transform: translateX(-100%);
			transition: transform 0.3s ease-in-out;
			border-right: 1px solid rgba(203, 33, 41, 0.3);
		}

		.mobile-menu.active {
			transform: translateX(0);
		}

		/* Overlay pour fermer le menu */
		.mobile-menu-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100vh;
			background: rgba(0, 0, 0, 0.5);
			z-index: 49;
			opacity: 0;
			visibility: hidden;
			transition: all 0.3s ease-in-out;
		}

		.mobile-menu-overlay.active {
			opacity: 1;
			visibility: visible;
		}

		.mobile-menu-content {
			display: flex;
			flex-direction: column;
			padding: 2rem 0;
			height: 100%;
		}

		.mobile-menu-header {
			padding: 0 1.5rem 1.5rem;
			border-bottom: 1px solid rgba(255, 255, 255, 0.1);
			margin-bottom: 1rem;
		}

		.mobile-menu-header img {
			width: 120px;
			height: auto;
		}

		.mobile-menu a {
			color: white;
			font-size: 1rem;
			font-weight: 500;
			text-decoration: none;
			padding: 0.875rem 1.5rem;
			transition: all 0.3s ease;
			border-left: 3px solid transparent;
			display: flex;
			align-items: center;
		}

		.mobile-menu a:hover,
		.mobile-menu a.text-punch-red {
			background: rgba(203, 33, 41, 0.1);
			border-left-color: #cb2129;
			color: #cb2129;
		}

		.mobile-menu a.bg-punch-red {
			background: #cb2129;
			color: white;
			margin: 1rem 1.5rem;
			border-radius: 0.5rem;
			border-left: none;
			justify-content: center;
			font-weight: 600;
		}

		.mobile-menu a:nth-child(1) { transition-delay: 0.1s; }
		.mobile-menu a:nth-child(2) { transition-delay: 0.2s; }
		.mobile-menu a:nth-child(3) { transition-delay: 0.3s; }
		.mobile-menu a:nth-child(4) { transition-delay: 0.4s; }
		.mobile-menu a:nth-child(5) { transition-delay: 0.5s; }
		.mobile-menu a:nth-child(6) { transition-delay: 0.6s; }

		.mobile-menu a:hover {
			color: #cb2129;
			background: rgba(203, 33, 41, 0.1);
		}

		.hamburger {
			display: none;
			flex-direction: column;
			cursor: pointer;
			padding: 0.5rem;
		}

		.hamburger span {
			width: 25px;
			height: 3px;
			background: white;
			margin: 3px 0;
			transition: 0.3s;
			border-radius: 2px;
		}

		.hamburger.active span:nth-child(1) {
			transform: rotate(-45deg) translate(-5px, 6px);
		}

		.hamburger.active span:nth-child(2) {
			opacity: 0;
		}

		.hamburger.active span:nth-child(3) {
			transform: rotate(45deg) translate(-5px, -6px);
		}

		/* Responsive */
		@media (max-width: 768px) {
			/* Menu hamburger visible sur mobile */
			.hamburger {
				display: flex;
			}

			/* Logo plus petit sur mobile */
			nav img {
				width: 100px !important;
			}

			/* Barre de réseaux sociaux responsive */
			.social-media-bar {
				right: 8px !important;
				padding-right: 0 !important;
				top: 50% !important;
				transform: translateY(-50%) !important;
			}

			.social-media-bar .flex {
				gap: 12px !important;
			}

			.social-button {
				width: 38px;
				height: 38px;
			}
			.social-button svg {
				width: 24px;
				height: 24px;
			}
		}

		@media (max-width: 480px) {
			/* Logo encore plus petit sur très petits écrans */
			nav img {
				width: 80px !important;
			}

			/* Très petits écrans */
			.social-media-bar {
				right: 4px !important;
			}

			.social-media-bar .flex {
				gap: 8px !important;
			}

			.social-button {
				width: 32px;
				height: 32px;
			}
			.social-button svg {
				width: 20px;
				height: 20px;
			}
		}
	</style>
</head>
<body class="min-h-screen text-white" style="font-family: 'Roboto', system-ui, -apple-system, 'Segoe UI', Ubuntu, Cantarell, 'Noto Sans', 'Helvetica Neue', Arial, sans-serif;">
	<!-- Hero Background Wrapper -->
	<div class="min-h-screen" style="background-image: url('imgs/background.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat; background-attachment: fixed;">
		<!-- Navbar -->
		<nav class="bg-transparent px-6 py-4 absolute top-0 left-0 w-full z-20">
			<div class="max-w-7xl mx-auto flex items-center justify-between">
				<a href="#" class="flex items-center space-x-3">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" class="w-32 h-auto drop-shadow-[0_0_20px_rgba(203,33,41,0.3)]" />
				</a>

				<!-- Menu Desktop -->
				<div class="hidden md:flex items-center space-x-6">
					<a href="index.html" class="hover:text-punch-red nav-link">Accueil</a>
					<a href="abonnements.html" class="hover:text-punch-red nav-link">Abonnements</a>
					<a href="coach.html" class="hover:text-punch-red nav-link">Coachs</a>
					<a href="planning.html" class="hover:text-punch-red nav-link">Planning</a>
					<a href="contact.html" class="hover:text-punch-red nav-link">Contact</a>
					<a href="register.php" class="px-4 py-2 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire</a>
				</div>

				<!-- Hamburger Menu Button -->
				<div class="hamburger md:hidden" id="hamburger">
					<span></span>
					<span></span>
					<span></span>
				</div>
			</div>
		</nav>

		<!-- Menu Mobile Overlay -->
		<div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

		<!-- Menu Mobile -->
		<div class="mobile-menu" id="mobileMenu">
			<div class="mobile-menu-content">
				<div class="mobile-menu-header">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" />
				</div>
				<a href="index.html" class="mobile-nav-link text-punch-red">Accueil</a>
				<a href="abonnements.html" class="mobile-nav-link">Abonnements</a>
				<a href="coach.html" class="mobile-nav-link">Coachs</a>
				<a href="planning.html" class="mobile-nav-link">Planning</a>
				<a href="contact.html" class="mobile-nav-link">Contact</a>
				<a href="register.php" class="mobile-nav-link bg-punch-red">S'inscrire</a>
			</div>
		</div>

		<!-- Hero -->
		<section class="max-w-5xl mx-auto px-6 pt-56 pb-24">
			<div class="flex flex-col items-center text-center gap-8">
				<div class="animate-fade-in">
					<h1 class="text-4xl md:text-6xl font-extrabold leading-tight">Transformez votre corps chez <span class="text-punch-red">Power Fitness</span></h1>
					<p class="mt-5 text-gray-300 text-lg max-w-3xl mx-auto">Un espace moderne, des coachs certifiés, des cours dynamiques et un suivi personnalisé pour atteindre vos objectifs.</p>
					<div class="mt-8 flex flex-wrap justify-center gap-4">
						<a href="register.php" class="px-6 py-3 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire maintenant</a>
						<a href="#abonnements" class="px-6 py-3 glass rounded-lg font-semibold hover:bg-white/20 transition">Découvrir nos offres</a>
					</div>
				</div>
			</div>
		</section>
	</div>

	<!-- À propos -->
	<section id="apropos" class="py-20">
		<div class="max-w-7xl mx-auto px-6 grid md:grid-cols-2 gap-10 items-center  p-4">
			<div class="reveal">
				<h2 class="section-title">À propos de Power Fitness</h2>
				<p class="section-subtitle mt-2">Notre mission: vous aider à devenir la meilleure version de vous-même.</p>
				<p class="text-gray-300 mt-6">Salle moderne équipée de machines dernières générations, espace cross, zone haltérophilie et studio pour cours collectifs. Ambiance motivante, coachs diplômés et suivi personnalisé.</p>
				<div class="mt-6 grid grid-cols-2 gap-4 text-sm text-gray-200">
					<div class="glass rounded-lg p-4 border border-white/20">Ouvert 7j/7</div>
					<div class="glass rounded-lg p-4 border border-white/20">Espace cardio & muscu</div>
					<div class="glass rounded-lg p-4 border border-white/20">Cours variés</div>
					<div class="glass rounded-lg p-4 border border-white/20">Coaching sur mesure</div>
				</div>
			</div>
			<div class="reveal">
				<div class="glass rounded-xl overflow-hidden border border-white/20">
					<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.19.jpeg" alt="Salle Power Fitness" class="w-full h-auto">
				</div>
			</div>
		</div>
	</section>

	<!-- Stats Counters -->
	<section class="py-16">
		<div class="max-w-7xl mx-auto px-6 grid grid-cols-2 md:grid-cols-4 gap-6">
			<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
				<div class="text-4xl font-extrabold text-punch-red" data-count="1200">0</div>
				<div class="text-gray-300 mt-2">Membres</div>
			</div>
			<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
				<div class="text-4xl font-extrabold text-punch-red" data-count="35">0</div>
				<div class="text-gray-300 mt-2">Cours/semaine</div>
			</div>
			<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
				<div class="text-4xl font-extrabold text-punch-red" data-count="12">0</div>
				<div class="text-gray-300 mt-2">Coachs</div>
			</div>
			<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
				<div class="text-4xl font-extrabold text-punch-red" data-count="7">0</div>
				<div class="text-gray-300 mt-2">Ans d'expérience</div>
			</div>
		</div>
	</section>

	<!-- Partners Marquee -->
	<section class="py-12">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title">Nos partenaires</h2>
			<div class="marquee mt-6 glass rounded-xl border border-white/20">
				<div class="marquee-track">
					<img src="imgs/LOGO POWER FITNESS.png" alt="logo" class="h-10 opacity-80" />
					<img src="imgs/LOGO POWER FITNESS.png" alt="logo" class="h-10 opacity-80" />
					<img src="imgs/LOGO POWER FITNESS.png" alt="logo" class="h-10 opacity-80" />
					<img src="imgs/LOGO POWER FITNESS.png" alt="logo" class="h-10 opacity-80" />
					<img src="imgs/LOGO POWER FITNESS.png" alt="logo" class="h-10 opacity-80" />
					<img src="imgs/LOGO POWER FITNESS.png" alt="logo" class="h-10 opacity-80" />
				</div>
			</div>
		</div>
	</section>

	<!-- Abonnements / Pricing -->
	<section id="abonnements" class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title">Nos abonnements</h2>
			<p class="section-subtitle mt-2">Choisissez la formule qui vous convient.</p>
			<div class="mt-10 grid md:grid-cols-3 gap-6">
				<div class="glass rounded-xl p-8 border border-white/20">
					<h3 class="text-xl font-bold">Mensuel</h3>
					<p class="mt-4 text-4xl font-extrabold text-punch-red">199 DH</p>
					<ul class="mt-6 space-y-3 text-gray-200 text-sm">
						<li>Accès illimité salle</li>
						<li>1 cours collectif/semaine</li>
						<li>Programme d'initiation</li>
					</ul>
					<a href="register.php" class="mt-6 inline-block px-5 py-3 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'abonner</a>
					<button type="button" class="mt-3 inline-block px-5 py-3 bg-white/10 border border-white/20 rounded-lg font-semibold hover:bg-white/20 transition details-btn" data-target="#plan-detail-mensuel">Plus de détails</button>
				</div>
				<div class="glass rounded-xl p-8 border border-white/20 ring-2 ring-punch-red">
					<h3 class="text-xl font-bold">Trimestriel</h3>
					<p class="mt-4 text-4xl font-extrabold text-punch-red">549 DH</p>
					<ul class="mt-6 space-y-3 text-gray-200 text-sm">
						<li>Accès illimité salle</li>
						<li>2 cours collectifs/semaine</li>
						<li>Bilan forme + suivi</li>
					</ul>
					<a href="register.php" class="mt-6 inline-block px-5 py-3 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'abonner</a>
					<button type="button" class="mt-3 inline-block px-5 py-3 bg-white/10 border border-white/20 rounded-lg font-semibold hover:bg-white/20 transition details-btn" data-target="#plan-detail-trimestriel">Plus de détails</button>
				</div>
				<div class="glass rounded-xl p-8 border border-white/20">
					<h3 class="text-xl font-bold">Annuel</h3>
					<p class="mt-4 text-4xl font-extrabold text-punch-red">1999 DH</p>
					<ul class="mt-6 space-y-3 text-gray-200 text-sm">
						<li>Accès illimité salle</li>
						<li>Cours illimités</li>
						<li>Coaching personnalisé</li>
					</ul>
					<a href="register.php" class="mt-6 inline-block px-5 py-3 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'abonner</a>
					<button type="button" class="mt-3 inline-block px-5 py-3 bg-white/10 border border-white/20 rounded-lg font-semibold hover:bg-white/20 transition details-btn" data-target="#plan-detail-annuel">Plus de détails</button>
				</div>
			</div>
			<!-- Hidden Detail Templates -->
			<div class="hidden">
				<div id="plan-detail-mensuel">
					<h3 class="text-2xl font-extrabold">Détails abonnement Mensuel</h3>
					<ul class="mt-4 space-y-2 text-gray-200 text-sm list-disc list-inside">
						<li>Accès illimité à la salle 7j/7</li>
						<li>1 cours collectif/semaine inclus, cours supplémentaires à tarif réduit</li>
						<li>Programme d'initiation offert et suivi de base</li>
						<li>Sans engagement, renouvelable chaque mois</li>
					</ul>
				</div>
				<div id="plan-detail-trimestriel">
					<h3 class="text-2xl font-extrabold">Détails abonnement Trimestriel</h3>
					<ul class="mt-4 space-y-2 text-gray-200 text-sm list-disc list-inside">
						<li>Accès illimité à la salle 7j/7</li>
						<li>2 cours collectifs/semaine inclus</li>
						<li>Bilan forme trimestriel et suivi personnalisé</li>
						<li>Tarif préférentiel sur le coaching individuel</li>
					</ul>
				</div>
				<div id="plan-detail-annuel">
					<h3 class="text-2xl font-extrabold">Détails abonnement Annuel</h3>
					<ul class="mt-4 space-y-2 text-gray-200 text-sm list-disc list-inside">
						<li>Accès illimité à la salle 7j/7</li>
						<li>Cours collectifs illimités</li>
						<li>Coaching personnalisé mensuel et bilan forme</li>
						<li>Cadeau de bienvenue et réductions partenaires</li>
					</ul>
				</div>
			</div>
		</div>
	</section>

	<!-- Cours / Classes -->
	<section id="cours" class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title">Cours populaires</h2>
			<p class="section-subtitle mt-2">Des séances pour tous les niveaux.</p>
			<div class="mt-10 grid md:grid-cols-3 gap-6">
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<h3 class="font-bold text-xl">Cross Training</h3>
					<p class="text-gray-300 mt-2">Intensité, force, cardio et dépassement de soi.</p>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<h3 class="font-bold text-xl">Boxe</h3>
					<p class="text-gray-300 mt-2">Technique, explosivité et confiance en soi.</p>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<h3 class="font-bold text-xl">Yoga</h3>
					<p class="text-gray-300 mt-2">Souplesse, respiration et bien-être.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Coachs -->
	<section id="coachs" class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title">Nos coachs</h2>
			<p class="section-subtitle mt-2">Une équipe passionnée et diplômée.</p>
			<div class="mt-10 grid md:grid-cols-3 gap-6">
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal" style="animation: floatSlow 6s ease-in-out infinite;">
					<div class="w-24 h-24 mx-auto rounded-full bg-white/10 flex items-center justify-center border border-white/20"><i class="fa-solid fa-person-running text-2xl text-punch-red"></i></div>
					<h3 class="mt-4 font-bold">Yassine</h3>
					<p class="text-gray-300 text-sm">Cross Training & Musculation</p>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal" style="animation: floatSlow 6s 1s ease-in-out infinite;">
					<div class="w-24 h-24 mx-auto rounded-full bg-white/10 flex items-center justify-center border border-white/20"><i class="fa-solid fa-hand-fist text-2xl text-punch-red"></i></div>
					<h3 class="mt-4 font-bold">Samir</h3>
					<p class="text-gray-300 text-sm">Boxe & Cardio</p>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal" style="animation: floatSlow 6s 2s ease-in-out infinite;">
					<div class="w-24 h-24 mx-auto rounded-full bg-white/10 flex items-center justify-center border border-white/20"><i class="fa-solid fa-spa text-2xl text-punch-red"></i></div>
					<h3 class="mt-4 font-bold">Imane</h3>
					<p class="text-gray-300 text-sm">Yoga & Mobilité</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Planning -->
	<section id="planning" class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title">Planning hebdomadaire</h2>
			<p class="section-subtitle mt-2">Organisez votre semaine d'entraînement.</p>
			<div class="mt-8 overflow-x-auto glass rounded-xl p-6 border border-white/20">
				<table class="min-w-full text-left text-sm">
					<thead class="text-gray-200">
						<tr>
							<th class="py-3 pr-6">Jour</th>
							<th class="py-3 pr-6">6:00</th>
							<th class="py-3 pr-6">10:00</th>
							<th class="py-3 pr-6">18:00</th>
							<th class="py-3 pr-6">20:00</th>
						</tr>
					</thead>
					<tbody class="text-gray-100">
						<tr>
							<td class="py-3 pr-6">Lundi</td><td class="py-3 pr-6">Cardio</td><td class="py-3 pr-6">—</td><td class="py-3 pr-6">Boxe</td><td class="py-3 pr-6">Yoga</td>
						</tr>
						<tr>
							<td class="py-3 pr-6">Mercredi</td><td class="py-3 pr-6">—</td><td class="py-3 pr-6">Cross</td><td class="py-3 pr-6">Muscu</td><td class="py-3 pr-6">Boxe</td>
						</tr>
						<tr>
							<td class="py-3 pr-6">Vendredi</td><td class="py-3 pr-6">Yoga</td><td class="py-3 pr-6">—</td><td class="py-3 pr-6">Cross</td><td class="py-3 pr-6">Cardio</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</section>

	<!-- Témoignages -->
	<section id="temoignages" class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title">Ils nous font confiance</h2>
			<p class="section-subtitle mt-2">Des membres satisfaits et motivés.</p>
			<div class="mt-10 grid md:grid-cols-3 gap-6">
				<div class="glass rounded-xl p-6 border border-white/20">
					<p class="text-gray-200">“Salle au top, ambiance motivante et équipements récents.”</p>
					<div class="mt-4 text-sm text-gray-400">— Karim</div>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<p class="text-gray-200">“Les coachs m'ont aidée à progresser rapidement.”</p>
					<div class="mt-4 text-sm text-gray-400">— Salma</div>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<p class="text-gray-200">“Cours variés, planning flexible, je recommande.”</p>
					<div class="mt-4 text-sm text-gray-400">— Mehdi</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Services -->
	<section id="services" class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title">Nos services</h2>
			<p class="section-subtitle mt-2">Tout ce qu'il faut pour progresser.</p>
			<div class="mt-10 grid md:grid-cols-4 gap-6">
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<i class="fa-solid fa-dumbbell text-punch-red text-2xl"></i>
					<h3 class="font-bold mt-3">Musculation</h3>
					<p class="text-gray-300 text-sm mt-2">Machines & charges libres.</p>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<i class="fa-solid fa-heart-pulse text-punch-red text-2xl"></i>
					<h3 class="font-bold mt-3">Cardio</h3>
					<p class="text-gray-300 text-sm mt-2">Tapis, vélos, rameurs.</p>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<i class="fa-solid fa-people-group text-punch-red text-2xl"></i>
					<h3 class="font-bold mt-3">Cours collectifs</h3>
					<p class="text-gray-300 text-sm mt-2">Yoga, boxe, cross, HIIT.</p>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<i class="fa-solid fa-user-check text-punch-red text-2xl"></i>
					<h3 class="font-bold mt-3">Coaching</h3>
					<p class="text-gray-300 text-sm mt-2">Programmes personnalisés.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Galerie -->
	<section id="galerie" class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title">Galerie</h2>
			<p class="section-subtitle mt-2">Aperçu de notre univers.</p>
			<div class="mt-10 grid grid-cols-2 md:grid-cols-4 gap-3">
				<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.20.jpeg" alt="Galerie 1" class="aspect-[4/3] w-full h-full object-cover rounded-lg hover:scale-[1.02] transition reveal" />
				<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.21.jpeg" alt="Galerie 2" class="aspect-[4/3] w-full h-full object-cover rounded-lg hover:scale-[1.02] transition reveal" />
				<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22.jpeg" alt="Galerie 3" class="aspect-[4/3] w-full h-full object-cover rounded-lg hover:scale-[1.02] transition reveal" />
				<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (1).jpeg" alt="Galerie 4" class="aspect-[4/3] w-full h-full object-cover rounded-lg hover:scale-[1.02] transition reveal" />
			</div>
		</div>
	</section>

	<!-- Contact -->
	<section id="contact" class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title">Contact</h2>
			<p class="section-subtitle mt-2">Besoin d'informations ? Écrivez-nous.</p>
			<div class="mt-8 grid md:grid-cols-2 gap-6">
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<h3 class="font-bold">Power Fitness</h3>
					<ul class="mt-4 space-y-2 text-gray-200 text-sm">
						<li><i class="fa-solid fa-location-dot text-punch-red mr-2"></i>Votre adresse ici</li>
						<li><i class="fa-solid fa-phone text-punch-red mr-2"></i>+212 6XX XX XX XX</li>
						<li><i class="fa-solid fa-envelope text-punch-red mr-2"></i><EMAIL></li>
					</ul>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<form class="space-y-4">
						<input type="text" placeholder="Nom" class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 outline-none focus:ring-2 focus:ring-punch-red">
						<input type="email" placeholder="Email" class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 outline-none focus:ring-2 focus:ring-punch-red">
						<textarea placeholder="Message" rows="4" class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 outline-none focus:ring-2 focus:ring-punch-red"></textarea>
						<button type="button" class="px-6 py-3 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">Envoyer</button>
					</form>
				</div>
			</div>
		</div>
	</section>

	<!-- FAQ -->
	<section id="faq" class="py-20">
		<div class="max-w-4xl mx-auto px-6">
			<h2 class="section-title text-center">FAQ</h2>
			<p class="section-subtitle mt-2 text-center">Questions fréquentes</p>
			<div class="mt-8 space-y-4">
				<div class="glass rounded-xl border border-white/20 p-5 reveal">
					<h3 class="font-semibold">Puis-je essayer avant de m'abonner ?</h3>
					<p class="text-gray-300 mt-2">Oui, une séance découverte est offerte. Passez à l'accueil.</p>
				</div>
				<div class="glass rounded-xl border border-white/20 p-5 reveal">
					<h3 class="font-semibold">Les cours sont-ils inclus ?</h3>
					<p class="text-gray-300 mt-2">Selon la formule choisie. Les cours illimités sont inclus dans l'annuel.</p>
				</div>
				<div class="glass rounded-xl border border-white/20 p-5 reveal">
					<h3 class="font-semibold">Horaires d'ouverture ?</h3>
					<p class="text-gray-300 mt-2">Du lundi au samedi, 6h à 22h. Dimanche, 9h à 18h.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Final CTA -->
	<section class="py-20">
		<div class="max-w-5xl mx-auto px-6 text-center glass rounded-2xl p-10 border border-white/20 reveal">
			<h2 class="text-3xl md:text-4xl font-extrabold">Prêt à rejoindre Power Fitness ?</h2>
			<p class="text-gray-300 mt-3">Inscrivez-vous dès maintenant et commencez votre transformation.</p>
			<a href="register.php" class="mt-6 inline-block px-8 py-4 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire</a>
		</div>
	</section>

	<!-- Footer -->
	<footer class="py-12 px-6" style="background-color: #000000;">
		<div class="container mx-auto max-w-6xl">
			<div class="grid md:grid-cols-4 gap-8 mb-8">
				<div class="md:col-span-1">
					<div class="flex items-center mb-4">
						<span class="text-2xl font-bold text-white">POWER</span>
						<span class="text-2xl font-bold text-brand-red">FITNESS</span>
					</div>
					<p class="text-gray-400 mb-4">Transform your limits. Unleash your power.</p>
					<div class="flex space-x-4">
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
							</svg>
						</a>
					</div>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Programs</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Strength Training</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Cardio Power</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Functional Fitness</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">HIIT Sessions</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Facility</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Equipment</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Amenities</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Hours</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Location</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Contact</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Join Now</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Book Tour</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Support</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Careers</a></li>
					</ul>
				</div>
			</div>

			<div class="border-t border-gray-800 pt-8 text-center">
				<p class="text-gray-400">&copy; 2025 Power Fitness. All rights reserved.</p>
			</div>
		</div>
	</footer>

	<!-- Barre de Réseaux Sociaux Flottante -->
	<div class="social-media-bar fixed right-4 top-1/2 transform -translate-y-1/2 z-50" style="padding-right: 10px;">
		<div class="flex flex-col space-y-5">
			<!-- WhatsApp -->
			<a href="https://api.whatsapp.com/send?phone=+212709702820" target="_blank" class="whatsapp-button social-button" onclick="this.href=window.innerWidth > 768 ? 'https://web.whatsapp.com/send?phone=+212709702820' : 'https://api.whatsapp.com/send?phone=+212709702820';">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.890-5.335 11.893-11.893A11.821 11.821 0 0020.525 3.687"></path>
				</svg>
			</a>

			<!-- Waze -->
			<a class="waze-button social-button" href="https://waze.com/ul?ll=33.5731,-7.5898&navigate=yes" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.568 14.832c-.054.85-.541 1.62-1.281 2.01-.74.39-1.621.39-2.361 0-.74-.39-1.227-1.16-1.281-2.01-.054-.85.324-1.674.993-2.162.669-.488 1.559-.488 2.228 0 .669.488 1.047 1.312.993 2.162zm-11.136 0c-.054.85-.541 1.62-1.281 2.01-.74.39-1.621.39-2.361 0-.74-.39-1.227-1.16-1.281-2.01-.054-.85.324-1.674.993-2.162.669-.488 1.559-.488 2.228 0 .669.488 1.047 1.312.993 2.162zM12 4.8c2.21 0 4 1.79 4 4s-1.79 4-4 4-4-1.79-4-4 1.79-4 4-4zm0 1.6c-1.324 0-2.4 1.076-2.4 2.4s1.076 2.4 2.4 2.4 2.4-1.076 2.4-2.4S13.324 6.4 12 6.4z"></path>
				</svg>
			</a>

			<!-- Facebook -->
			<a class="facebook-button social-button" href="https://www.facebook.com/powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"></path>
				</svg>
			</a>

			<!-- TikTok -->
			<a class="tiktok-button social-button" href="https://www.tiktok.com/@powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"></path>
				</svg>
			</a>

			<!-- Instagram -->
			<a class="instagram-button social-button" href="https://www.instagram.com/powerfitness/" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path>
				</svg>
			</a>
		</div>
	</div>

	<!-- Pricing Details Top-Right Panel -->
	<div id="pricing-panel" class="fixed top-6 right-6 z-40 max-w-md w-[90vw] md:w-[28rem] invisible opacity-0 translate-y-2 transition-all duration-300">
		<div class="glass rounded-2xl p-6 border border-white/20 shadow-2xl">
			<div class="flex items-start justify-between">
				<h3 id="pricing-panel-title" class="text-2xl font-extrabold">Détails</h3>
				<button id="pricing-panel-close" class="text-white/80 hover:text-white"><i class="fa-solid fa-xmark text-2xl"></i></button>
			</div>
			<div id="pricing-panel-body" class="mt-4"></div>
		</div>
	</div>

	<!-- Scripts: reveal-on-scroll, counters, parallax -->
	<script>
		(function() {
			// Menu Mobile Toggle
			const hamburger = document.getElementById('hamburger');
			const mobileMenu = document.getElementById('mobileMenu');
			const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
			const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

			function openMobileMenu() {
				hamburger.classList.add('active');
				mobileMenu.classList.add('active');
				mobileMenuOverlay.classList.add('active');
				document.body.style.overflow = 'hidden';
			}

			function closeMobileMenu() {
				hamburger.classList.remove('active');
				mobileMenu.classList.remove('active');
				mobileMenuOverlay.classList.remove('active');
				document.body.style.overflow = '';
			}

			hamburger.addEventListener('click', function() {
				if (mobileMenu.classList.contains('active')) {
					closeMobileMenu();
				} else {
					openMobileMenu();
				}
			});

			// Fermer le menu mobile quand on clique sur l'overlay
			mobileMenuOverlay.addEventListener('click', closeMobileMenu);

			// Fermer le menu mobile quand on clique sur un lien
			mobileNavLinks.forEach(link => {
				link.addEventListener('click', closeMobileMenu);
			});

			// Fermer le menu mobile avec la touche Escape
			document.addEventListener('keydown', function(e) {
				if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
					closeMobileMenu();
				}
			});

			// Smooth scroll for nav links
			document.querySelectorAll('a[href^="#"]').forEach(anchor => {
				anchor.addEventListener('click', function (e) {
					const href = this.getAttribute('href');
					if (!href || href === '#') return;
					e.preventDefault();
					document.querySelector(href)?.scrollIntoView({ behavior: 'smooth', block: 'start' });
				});
			});

			// Reveal on scroll
			const revealEls = Array.from(document.querySelectorAll('.reveal'));
			const observer = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						entry.target.classList.add('visible');
						observer.unobserve(entry.target);
					}
				});
			}, { threshold: 0.15 });
			revealEls.forEach(el => observer.observe(el));

			// Count-up
			function animateCount(el) {
				const target = parseInt(el.getAttribute('data-count') || '0', 10);
				let start = 0;
				const duration = 1200;
				const startTs = performance.now();
				function step(ts) {
					const progress = Math.min((ts - startTs) / duration, 1);
					const val = Math.floor(progress * target);
					el.textContent = val.toString();
					if (progress < 1) requestAnimationFrame(step);
				}
				requestAnimationFrame(step);
			}
			const counters = document.querySelectorAll('[data-count]');
			const counterObs = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						animateCount(entry.target);
						counterObs.unobserve(entry.target);
					}
				});
			}, { threshold: 0.5 });
			counters.forEach(el => counterObs.observe(el));

			// Simple parallax on hero features card
			const heroCard = document.querySelector('.animate-slide-up');
			if (heroCard) {
				window.addEventListener('scroll', () => {
					const y = window.scrollY;
					heroCard.style.transform = `translateY(${Math.min(0, -y * 0.02)}px)`;
				});
			}
			// Active link highlight on scroll
			const sections = document.querySelectorAll('section[id]');
			const navLinks = document.querySelectorAll('.nav-link');
			function setActive() {
				let current = '';
				sections.forEach(sec => {
					const top = window.scrollY;
					const offset = sec.offsetTop - 120;
					const height = sec.offsetHeight;
					if (top >= offset && top < offset + height) current = '#' + sec.id;
				});
				navLinks.forEach(link => {
					if (link.getAttribute('href') === current) link.classList.add('text-punch-red');
					else link.classList.remove('text-punch-red');
				});
			}
			window.addEventListener('scroll', setActive);
			setActive();
		})();

		// Pricing details top-right panel
		(function() {
			const panel = document.getElementById('pricing-panel');
			const panelTitle = document.getElementById('pricing-panel-title');
			const panelBody = document.getElementById('pricing-panel-body');
			const closeBtn = document.getElementById('pricing-panel-close');
			const buttons = document.querySelectorAll('.details-btn');
			const pricingSection = document.getElementById('abonnements');

			function showPanel(title, html) {
				if (!panel || !panelTitle || !panelBody) return;
				panelTitle.textContent = title;
				panelBody.innerHTML = html;
				panel.classList.remove('invisible');
				panel.style.opacity = '1';
				panel.style.transform = 'translateY(0)';
			}

			function hidePanel() {
				if (!panel) return;
				panel.style.opacity = '0';
				panel.style.transform = 'translateY(8px)';
				setTimeout(() => panel.classList.add('invisible'), 250);
			}

			buttons.forEach(btn => {
				const card = btn.closest('.glass');
				btn.addEventListener('click', (e) => {
					e.stopPropagation();
					const selector = btn.getAttribute('data-target');
					const template = selector ? document.querySelector(selector) : null;
					if (!template) return;
					const title = template.querySelector('h3')?.textContent || 'Détails';
					showPanel(title, template.innerHTML);
				});

				if (card) {
					card.addEventListener('mouseleave', () => {
						hidePanel();
					});
				}
			});

			if (closeBtn) closeBtn.addEventListener('click', hidePanel);
			// Hide panel if leaving pricing section area
			if (pricingSection) pricingSection.addEventListener('mouseleave', hidePanel);
			window.addEventListener('scroll', () => { if (panel && !panel.classList.contains('invisible')) { /* keep visible */ } });
		})();
	</script>
</body>
</html>

