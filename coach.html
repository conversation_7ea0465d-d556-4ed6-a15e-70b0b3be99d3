<!DOCTYPE html>
<html lang="fr">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<title>Nos Coachs - Power Fitness</title>
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
	<script src="https://cdn.tailwindcss.com"></script>
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
	<link rel="icon" type="image/png" href="imgs/LOGO POWER FITNESS.png" />
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						'punch-red': '#CB2129',
						'punch-white': '#FFFFFF',
						'punch-black': '#000000',
					},
					fontFamily: {
						'punch': ['Roboto', 'system-ui', '-apple-system', 'Segoe UI', 'Ubuntu', 'Cantarell', 'Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
					},
					animation: {
						'fade-in': 'fadeIn 0.6s ease-out',
						'slide-up': 'slideUp 0.8s ease-out',
						'bounce-slow': 'bounce 2s infinite',
						'pulse-slow': 'pulse 3s infinite',
						'wiggle': 'wiggle 1s ease-in-out infinite',
						'heart-beat': 'heartBeat 0.6s ease-in-out',
						'like-pop': 'likePop 0.3s ease-out',
					}
				}
			}
		}
	</script>
	<style>
		@keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes slideUp { from { opacity: 0; transform: translateY(40px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes floatSlow { 0% { transform: translateY(0px); } 50% { transform: translateY(-10px); } 100% { transform: translateY(0px); } }
		@keyframes wiggle { 0%, 7% { transform: rotateZ(0); } 15% { transform: rotateZ(-15deg); } 20% { transform: rotateZ(10deg); } 25% { transform: rotateZ(-10deg); } 30% { transform: rotateZ(6deg); } 35% { transform: rotateZ(-4deg); } 40%, 100% { transform: rotateZ(0); } }
		@keyframes glow { 0% { box-shadow: 0 0 20px rgba(203, 33, 41, 0.3); } 50% { box-shadow: 0 0 40px rgba(203, 33, 41, 0.6); } 100% { box-shadow: 0 0 20px rgba(203, 33, 41, 0.3); } }
		@keyframes heartBeat { 0% { transform: scale(1); } 50% { transform: scale(1.3); } 100% { transform: scale(1); } }
		@keyframes likePop { 0% { transform: scale(1); } 50% { transform: scale(1.5); } 100% { transform: scale(1); } }
		@keyframes shimmer { 0% { background-position: -200% 0; } 100% { background-position: 200% 0; } }
		@keyframes cardFloat { 0%, 100% { transform: translateY(0px) rotateY(0deg); } 50% { transform: translateY(-15px) rotateY(5deg); } }

		body {
			background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
			background-attachment: fixed;
			position: relative;
			overflow-x: hidden;
			font-family: 'punch', system-ui, sans-serif;
		}

		body::before {
			content: '';
			position: fixed; inset: 0;
			background: radial-gradient(circle at 20% 20%, rgba(203, 33, 41, 0.15) 0%, transparent 50%),
			radial-gradient(circle at 80% 80%, rgba(203, 33, 41, 0.1) 0%, transparent 50%);
			pointer-events: none; z-index: 0;
		}

		.glass { backdrop-filter: blur(10px); background: rgba(255,255,255,0.12); border: 1px solid rgba(255,255,255,0.2); }
		.section-title { color: #ffffff; font-weight: 800; font-size: 1.875rem; line-height: 2.25rem; }
		@media (min-width: 768px) { .section-title { font-size: 2.25rem; line-height: 2.5rem; } }
		.section-subtitle { color: #d1d5db; }

		.reveal { opacity: 0; transform: translateY(20px); transition: opacity .6s ease, transform .6s ease; }
		.reveal.visible { opacity: 1; transform: translateY(0); }

		.coach-card { 
			transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); 
			position: relative;
			overflow: hidden;
		}
		.coach-card::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
			transition: left 0.5s;
		}
		.coach-card:hover::before {
			left: 100%;
		}
		.coach-card:hover { 
			transform: translateY(-15px) scale(1.03) rotateY(3deg); 
			animation: glow 2s infinite;
			box-shadow: 0 20px 40px rgba(203, 33, 41, 0.3);
		}

		.coach-image {
			transition: all 0.3s ease;
			position: relative;
			overflow: hidden;
		}
		.coach-image::after {
			content: '';
			position: absolute;
			inset: 0;
			background: linear-gradient(45deg, transparent 30%, rgba(203, 33, 41, 0.1) 50%, transparent 70%);
			opacity: 0;
			transition: opacity 0.3s ease;
		}
		.coach-card:hover .coach-image::after {
			opacity: 1;
		}
		.coach-card:hover .coach-image img {
			transform: scale(1.1);
		}

		.like-button {
			transition: all 0.3s ease;
			cursor: pointer;
			user-select: none;
		}
		.like-button:hover {
			transform: scale(1.1);
		}
		.like-button.liked {
			color: #ef4444;
			animation: heart-beat 0.6s ease-in-out;
		}
		.like-button.liked i {
			animation: like-pop 0.3s ease-out;
		}

		.specialty-badge {
			background: linear-gradient(45deg, #CB2129, #ff4757);
			animation: shimmer 3s infinite;
			background-size: 200% 200%;
		}

		.social-icon {
			transition: all 0.3s ease;
		}
		.social-icon:hover {
			transform: translateY(-3px) scale(1.2);
			color: #CB2129;
		}

		/* Barre de Réseaux Sociaux */
		.social-button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 45px;
			height: 45px;
			border-radius: 50%;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
			position: relative;
			overflow: hidden;
		}

		.whatsapp-button {
			background: #CB2129;
		}

		.waze-button {
			background: #000000;
		}

		.facebook-button {
			background: #CB2129;
		}

		.tiktok-button {
			background: #000000;
		}

		.instagram-button {
			background: #CB2129;
		}

		.social-button:hover {
			transform: scale(1.1);
			box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
		}

		.social-button::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
			transition: left 0.5s;
		}

		.social-button:hover::before {
			left: 100%;
		}

		/* Animation d'apparition */
		.social-button {
			animation: slideInRight 0.6s ease-out forwards;
			opacity: 0;
			transform: translateX(100px);
		}

		.social-button:nth-child(1) { animation-delay: 0.1s; }
		.social-button:nth-child(2) { animation-delay: 0.2s; }
		.social-button:nth-child(3) { animation-delay: 0.3s; }
		.social-button:nth-child(4) { animation-delay: 0.4s; }
		.social-button:nth-child(5) { animation-delay: 0.5s; }

		@keyframes slideInRight {
			to {
				opacity: 1;
				transform: translateX(0);
			}
		}

		/* Menu Mobile - Sidebar Style */
		.mobile-menu {
			position: fixed;
			top: 0;
			left: 0;
			width: 220px;
			height: 100vh;
			background: rgba(0, 0, 0, 0.95);
			backdrop-filter: blur(15px);
			z-index: 50;
			transform: translateX(-100%);
			transition: transform 0.3s ease-in-out;
			border-right: 1px solid rgba(203, 33, 41, 0.3);
		}

		.mobile-menu.active {
			transform: translateX(0);
		}

		/* Overlay pour fermer le menu */
		.mobile-menu-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100vh;
			background: rgba(0, 0, 0, 0.5);
			z-index: 49;
			opacity: 0;
			visibility: hidden;
			transition: all 0.3s ease-in-out;
		}

		.mobile-menu-overlay.active {
			opacity: 1;
			visibility: visible;
		}

		.mobile-menu-content {
			display: flex;
			flex-direction: column;
			padding: 2rem 0;
			height: 100%;
		}

		.mobile-menu-header {
			padding: 0 1.5rem 1.5rem;
			border-bottom: 1px solid rgba(255, 255, 255, 0.1);
			margin-bottom: 1rem;
		}

		.mobile-menu-header img {
			width: 120px;
			height: auto;
		}

		.mobile-menu a {
			color: white;
			font-size: 1rem;
			font-weight: 500;
			text-decoration: none;
			padding: 0.875rem 1.5rem;
			transition: all 0.3s ease;
			border-left: 3px solid transparent;
			display: flex;
			align-items: center;
		}

		.mobile-menu a:hover,
		.mobile-menu a.text-punch-red {
			background: rgba(203, 33, 41, 0.1);
			border-left-color: #cb2129;
			color: #cb2129;
		}

		.mobile-menu a.bg-punch-red {
			background: #cb2129;
			color: white;
			margin: 1rem 1.5rem;
			border-radius: 0.5rem;
			border-left: none;
			justify-content: center;
			font-weight: 600;
		}

		.mobile-menu a:nth-child(1) { transition-delay: 0.1s; }
		.mobile-menu a:nth-child(2) { transition-delay: 0.2s; }
		.mobile-menu a:nth-child(3) { transition-delay: 0.3s; }
		.mobile-menu a:nth-child(4) { transition-delay: 0.4s; }
		.mobile-menu a:nth-child(5) { transition-delay: 0.5s; }
		.mobile-menu a:nth-child(6) { transition-delay: 0.6s; }

		.mobile-menu a:hover {
			color: #cb2129;
			background: rgba(203, 33, 41, 0.1);
		}

		.hamburger {
			display: none;
			flex-direction: column;
			cursor: pointer;
			padding: 0.5rem;
		}

		.hamburger span {
			width: 25px;
			height: 3px;
			background: white;
			margin: 3px 0;
			transition: 0.3s;
			border-radius: 2px;
		}

		.hamburger.active span:nth-child(1) {
			transform: rotate(-45deg) translate(-5px, 6px);
		}

		.hamburger.active span:nth-child(2) {
			opacity: 0;
		}

		.hamburger.active span:nth-child(3) {
			transform: rotate(45deg) translate(-5px, -6px);
		}

		/* Responsive */
		@media (max-width: 768px) {
			/* Menu hamburger visible sur mobile */
			.hamburger {
				display: flex;
			}

			/* Logo plus petit sur mobile */
			nav img {
				width: 100px !important;
			}
			/* Barre de réseaux sociaux responsive */
			.social-media-bar {
				right: 8px !important;
				padding-right: 0 !important;
				top: 50% !important;
				transform: translateY(-50%) !important;
			}

			.social-media-bar .flex {
				gap: 12px !important;
			}

			.social-button {
				width: 38px;
				height: 38px;
			}
			.social-button svg {
				width: 24px;
				height: 24px;
			}
		}

		@media (max-width: 480px) {
			/* Logo encore plus petit sur très petits écrans */
			nav img {
				width: 80px !important;
			}

			/* Très petits écrans */
			.social-media-bar {
				right: 4px !important;
			}

			.social-media-bar .flex {
				gap: 8px !important;
			}

			.social-button {
				width: 32px;
				height: 32px;
			}
			.social-button svg {
				width: 20px;
				height: 20px;
			}
		}
	</style>
</head>
<body class="min-h-screen text-white">
	<!-- Hero Background -->
	<div class="min-h-screen" style="background-image: url('imgs/background.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat; background-attachment: fixed;">
		<!-- Navbar -->
		<nav class="bg-transparent px-6 py-4 absolute top-0 left-0 w-full z-20">
			<div class="max-w-7xl mx-auto flex items-center justify-between">
				<a href="index.html" class="flex items-center space-x-3">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" class="w-32 h-auto drop-shadow-[0_0_20px_rgba(203,33,41,0.3)]" />
				</a>

				<!-- Menu Desktop -->
				<div class="hidden md:flex items-center space-x-6">
					<a href="index.html" class="hover:text-punch-red nav-link">Accueil</a>
					<a href="abonnements.html" class="hover:text-punch-red nav-link">Abonnements</a>
					<a href="coach.html" class="text-punch-red nav-link">Coachs</a>
					<a href="planning.html" class="hover:text-punch-red nav-link">Planning</a>
					<a href="contact.html" class="hover:text-punch-red nav-link">Contact</a>
					<a href="register.php" class="px-4 py-2 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire</a>
				</div>

				<!-- Hamburger Menu Button -->
				<div class="hamburger md:hidden" id="hamburger">
					<span></span>
					<span></span>
					<span></span>
				</div>
			</div>
		</nav>

		<!-- Menu Mobile Overlay -->
		<div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

		<!-- Menu Mobile -->
		<div class="mobile-menu" id="mobileMenu">
			<div class="mobile-menu-content">
				<div class="mobile-menu-header">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" />
				</div>
				<a href="index.html" class="mobile-nav-link">Accueil</a>
				<a href="abonnements.html" class="mobile-nav-link">Abonnements</a>
				<a href="coach.html" class="mobile-nav-link text-punch-red">Coachs</a>
				<a href="planning.html" class="mobile-nav-link">Planning</a>
				<a href="contact.html" class="mobile-nav-link">Contact</a>
				<a href="register.php" class="mobile-nav-link bg-punch-red">S'inscrire</a>
			</div>
		</div>

		<!-- Hero Section -->
		<section class="max-w-5xl mx-auto px-6 pt-56 pb-24">
			<div class="flex flex-col items-center text-center gap-8">
				<div class="animate-fade-in">
					<h1 class="text-4xl md:text-6xl font-extrabold leading-tight">Nos <span class="text-punch-red">Coachs Experts</span></h1>
					<p class="mt-5 text-gray-300 text-lg max-w-3xl mx-auto">Rencontrez notre équipe de professionnels passionnés, certifiés et dévoués à votre réussite.</p>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 shadow-xl animate-slide-up">
					<div class="grid grid-cols-3 gap-6 text-center">
						<div><i class="fa-solid fa-medal text-punch-red text-2xl mb-2 block animate-wiggle"></i><span class="text-sm">Certifiés</span></div>
						<div><i class="fa-solid fa-heart text-punch-red text-2xl mb-2 block animate-pulse"></i><span class="text-sm">Passionnés</span></div>
						<div><i class="fa-solid fa-trophy text-punch-red text-2xl mb-2 block animate-bounce-slow"></i><span class="text-sm">Expérimentés</span></div>
					</div>
				</div>
			</div>
		</section>
	</div>

	<!-- Coaches Section -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Notre Équipe de Coachs</h2>
				<p class="section-subtitle mt-4">Des professionnels dédiés à votre transformation</p>
			</div>
			
			<div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
				<!-- Coach Yassine -->
				<div class="coach-card glass rounded-2xl overflow-hidden border border-white/20 reveal" style="animation: floatSlow 6s ease-in-out infinite;">
					<div class="coach-image relative">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.20 (3).jpeg" alt="Coach Yassine" class="w-full h-64 object-cover">
						<div class="absolute top-4 right-4 like-button" onclick="toggleLike(this, 'yassine')">
							<i class="fa-solid fa-heart text-2xl text-white/70 hover:text-red-500"></i>
							<span class="like-count ml-1 text-sm font-bold">127</span>
						</div>
						<div class="specialty-badge absolute bottom-4 left-4 px-3 py-1 rounded-full text-white text-xs font-bold">
							Cross Training Expert
						</div>
					</div>
					<div class="p-6">
						<div class="flex items-center justify-between mb-4">
							<h3 class="text-2xl font-bold">Yassine</h3>
							<div class="flex space-x-2">
								<i class="fa-brands fa-instagram social-icon text-gray-400 cursor-pointer"></i>
								<i class="fa-brands fa-facebook social-icon text-gray-400 cursor-pointer"></i>
								<i class="fa-brands fa-tiktok social-icon text-gray-400 cursor-pointer"></i>
							</div>
						</div>
						<p class="text-punch-red font-semibold mb-3">Spécialiste Cross Training & HIIT</p>
						<p class="text-gray-300 text-sm mb-4">5 ans d'expérience • Certifié CrossFit Level 2 • Champion régional de fitness fonctionnel</p>
						<div class="flex flex-wrap gap-2 mb-4">
							<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Cross Training</span>
							<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">HIIT</span>
							<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Musculation</span>
						</div>
						<div class="flex items-center justify-between text-sm text-gray-400">
							<span><i class="fa-solid fa-star text-yellow-400 mr-1"></i>4.9/5</span>
							<span><i class="fa-solid fa-users mr-1"></i>89 membres</span>
						</div>
					</div>
				</div>

				<!-- Coach Samir -->
				<div class="coach-card glass rounded-2xl overflow-hidden border border-white/20 reveal" style="animation: floatSlow 6s 0.5s ease-in-out infinite;">
					<div class="coach-image relative">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.21 (1).jpeg" alt="Coach Samir" class="w-full h-64 object-cover">
						<div class="absolute top-4 right-4 like-button" onclick="toggleLike(this, 'samir')">
							<i class="fa-solid fa-heart text-2xl text-white/70 hover:text-red-500"></i>
							<span class="like-count ml-1 text-sm font-bold">203</span>
						</div>
						<div class="specialty-badge absolute bottom-4 left-4 px-3 py-1 rounded-full text-white text-xs font-bold">
							Boxe & Self-Défense
						</div>
					</div>
					<div class="p-6">
						<div class="flex items-center justify-between mb-4">
							<h3 class="text-2xl font-bold">Samir</h3>
							<div class="flex space-x-2">
								<i class="fa-brands fa-instagram social-icon text-gray-400 cursor-pointer"></i>
								<i class="fa-brands fa-facebook social-icon text-gray-400 cursor-pointer"></i>
								<i class="fa-brands fa-youtube social-icon text-gray-400 cursor-pointer"></i>
							</div>
						</div>
						<p class="text-punch-red font-semibold mb-3">Champion de Boxe & Fitness</p>
						<p class="text-gray-300 text-sm mb-4">8 ans d'expérience • Champion régional de boxe • Instructeur certifié self-défense</p>
						<div class="flex flex-wrap gap-2 mb-4">
							<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Boxe</span>
							<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Self-Défense</span>
							<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Cardio</span>
						</div>
						<div class="flex items-center justify-between text-sm text-gray-400">
							<span><i class="fa-solid fa-star text-yellow-400 mr-1"></i>4.8/5</span>
							<span><i class="fa-solid fa-users mr-1"></i>76 membres</span>
						</div>
					</div>
				</div>

				<!-- Coach Imane -->
				<div class="coach-card glass rounded-2xl overflow-hidden border border-white/20 reveal" style="animation: floatSlow 6s 1s ease-in-out infinite;">
					<div class="coach-image relative">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.20 (4).jpeg" alt="Coach Imane" class="w-full h-64 object-cover object-top">
						<div class="absolute top-4 right-4 like-button" onclick="toggleLike(this, 'imane')">
							<i class="fa-solid fa-heart text-2xl text-white/70 hover:text-red-500"></i>
							<span class="like-count ml-1 text-sm font-bold">156</span>
						</div>
						<div class="specialty-badge absolute bottom-4 left-4 px-3 py-1 rounded-full text-white text-xs font-bold">
							Yoga & Bien-être
						</div>
					</div>
					<div class="p-6">
						<div class="flex items-center justify-between mb-4">
							<h3 class="text-2xl font-bold">Imane</h3>
							<div class="flex space-x-2">
								<i class="fa-brands fa-instagram social-icon text-gray-400 cursor-pointer"></i>
								<i class="fa-brands fa-linkedin social-icon text-gray-400 cursor-pointer"></i>
								<i class="fa-brands fa-tiktok social-icon text-gray-400 cursor-pointer"></i>
							</div>
						</div>
						<p class="text-punch-red font-semibold mb-3">Maître Yoga & Pilates</p>
						<p class="text-gray-300 text-sm mb-4">6 ans d'expérience • Certifiée Yoga Alliance 500h • Spécialiste méditation et bien-être</p>
						<div class="flex flex-wrap gap-2 mb-4">
							<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Yoga</span>
							<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Pilates</span>
							<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Méditation</span>
						</div>
						<div class="flex items-center justify-between text-sm text-gray-400">
							<span><i class="fa-solid fa-star text-yellow-400 mr-1"></i>5.0/5</span>
							<span><i class="fa-solid fa-users mr-1"></i>94 membres</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Nos Spécialités -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Nos Spécialités d'Entraînement</h2>
				<p class="section-subtitle mt-4">Découvrez nos différents programmes et équipements</p>
			</div>

			<div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
				<!-- Musculation -->
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal" style="animation: floatSlow 6s 1.5s ease-in-out infinite;">
					<div class="relative mb-4">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (2).jpeg" alt="Musculation" class="w-full h-32 object-cover rounded-lg border-2 border-punch-red">
					</div>
					<h3 class="font-bold text-lg mb-2">Musculation</h3>
					<p class="text-punch-red text-sm mb-3">Équipements modernes</p>
					<div class="flex justify-center space-x-1 mb-3">
						<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Haltères</span>
						<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Machines</span>
					</div>
					<div class="text-xs text-gray-400">
						<i class="fa-solid fa-dumbbell text-punch-red mr-1"></i>Zone dédiée
					</div>
				</div>

				<!-- Cardio Training -->
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal" style="animation: floatSlow 6s 2s ease-in-out infinite;">
					<div class="relative mb-4">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (3).jpeg" alt="Cardio Training" class="w-full h-32 object-cover rounded-lg border-2 border-punch-red">
					</div>
					<h3 class="font-bold text-lg mb-2">Cardio Training</h3>
					<p class="text-punch-red text-sm mb-3">Endurance & Performance</p>
					<div class="flex justify-center space-x-1 mb-3">
						<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Tapis</span>
						<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Vélos</span>
					</div>
					<div class="text-xs text-gray-400">
						<i class="fa-solid fa-heart-pulse text-punch-red mr-1"></i>Haute intensité
					</div>
				</div>

				<!-- Cross Training -->
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal" style="animation: floatSlow 6s 2.5s ease-in-out infinite;">
					<div class="relative mb-4">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (4).jpeg" alt="Cross Training" class="w-full h-32 object-cover rounded-lg border-2 border-punch-red">
					</div>
					<h3 class="font-bold text-lg mb-2">Cross Training</h3>
					<p class="text-punch-red text-sm mb-3">Entraînement fonctionnel</p>
					<div class="flex justify-center space-x-1 mb-3">
						<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Kettlebells</span>
						<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">TRX</span>
					</div>
					<div class="text-xs text-gray-400">
						<i class="fa-solid fa-fire text-punch-red mr-1"></i>Intensité max
					</div>
				</div>

				<!-- Cours Collectifs -->
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal" style="animation: floatSlow 6s 3s ease-in-out infinite;">
					<div class="relative mb-4">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (5).jpeg" alt="Cours Collectifs" class="w-full h-32 object-cover rounded-lg border-2 border-punch-red">
					</div>
					<h3 class="font-bold text-lg mb-2">Cours Collectifs</h3>
					<p class="text-punch-red text-sm mb-3">Motivation de groupe</p>
					<div class="flex justify-center space-x-1 mb-3">
						<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Yoga</span>
						<span class="bg-punch-red/20 text-punch-red px-2 py-1 rounded-full text-xs">Boxe</span>
					</div>
					<div class="text-xs text-gray-400">
						<i class="fa-solid fa-users text-punch-red mr-1"></i>Esprit d'équipe
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Coach Stats -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="glass rounded-2xl p-8 border border-white/20 reveal">
				<h2 class="section-title text-center mb-8">Pourquoi Nos Coachs Sont Exceptionnels</h2>

				<div class="grid md:grid-cols-4 gap-6">
					<div class="text-center">
						<div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-punch-red to-red-700 flex items-center justify-center">
							<i class="fa-solid fa-graduation-cap text-2xl text-white"></i>
						</div>
						<h3 class="font-bold text-lg mb-2">100% Certifiés</h3>
						<p class="text-gray-300 text-sm">Tous nos coachs possèdent des certifications reconnues</p>
					</div>

					<div class="text-center">
						<div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-punch-red to-red-700 flex items-center justify-center">
							<i class="fa-solid fa-heart text-2xl text-white animate-pulse"></i>
						</div>
						<h3 class="font-bold text-lg mb-2">Passionnés</h3>
						<p class="text-gray-300 text-sm">Une équipe motivée et dévouée à votre réussite</p>
					</div>

					<div class="text-center">
						<div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-punch-red to-red-700 flex items-center justify-center">
							<i class="fa-solid fa-users text-2xl text-white"></i>
						</div>
						<h3 class="font-bold text-lg mb-2">Suivi Personnalisé</h3>
						<p class="text-gray-300 text-sm">Accompagnement individuel adapté à vos objectifs</p>
					</div>

					<div class="text-center">
						<div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-punch-red to-red-700 flex items-center justify-center">
							<i class="fa-solid fa-trophy text-2xl text-white animate-wiggle"></i>
						</div>
						<h3 class="font-bold text-lg mb-2">Résultats Prouvés</h3>
						<p class="text-gray-300 text-sm">Des milliers de transformations réussies</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="py-20">
		<div class="max-w-5xl mx-auto px-6 text-center glass rounded-2xl p-10 border border-white/20 reveal">
			<h2 class="text-3xl md:text-4xl font-extrabold mb-4">Prêt à Rencontrer Nos Coachs ?</h2>
			<p class="text-gray-300 mb-8">Rejoignez Power Fitness et bénéficiez de l'expertise de nos professionnels</p>
			<div class="flex flex-wrap justify-center gap-4">
				<a href="register.php" class="px-8 py-4 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire maintenant</a>
				<a href="planning.html" class="px-8 py-4 glass border border-white/20 rounded-lg font-semibold hover:bg-white/20 transition">Voir les cours</a>
			</div>
		</div>
	</section>

	<!-- Footer -->
	<footer class="py-12 px-6" style="background-color: #000000;">
		<div class="container mx-auto max-w-6xl">
			<div class="grid md:grid-cols-4 gap-8 mb-8">
				<div class="md:col-span-1">
					<div class="flex items-center mb-4">
						<span class="text-2xl font-bold text-white">POWER</span>
						<span class="text-2xl font-bold text-brand-red">FITNESS</span>
					</div>
					<p class="text-gray-400 mb-4">Transform your limits. Unleash your power.</p>
					<div class="flex space-x-4">
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
							</svg>
						</a>
					</div>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Programs</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Strength Training</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Cardio Power</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Functional Fitness</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">HIIT Sessions</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Facility</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Equipment</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Amenities</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Hours</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Location</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Contact</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Join Now</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Book Tour</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Support</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Careers</a></li>
					</ul>
				</div>
			</div>

			<div class="border-t border-gray-800 pt-8 text-center">
				<p class="text-gray-400">&copy; 2025 Power Fitness. All rights reserved.</p>
			</div>
		</div>
	</footer>

	<!-- WhatsApp Help Button -->


	<!-- Scripts -->
	<script>
		// Menu Mobile Toggle
		const hamburger = document.getElementById('hamburger');
		const mobileMenu = document.getElementById('mobileMenu');
		const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
		const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

		function openMobileMenu() {
			hamburger.classList.add('active');
			mobileMenu.classList.add('active');
			mobileMenuOverlay.classList.add('active');
			document.body.style.overflow = 'hidden';
		}

		function closeMobileMenu() {
			hamburger.classList.remove('active');
			mobileMenu.classList.remove('active');
			mobileMenuOverlay.classList.remove('active');
			document.body.style.overflow = '';
		}

		hamburger.addEventListener('click', function() {
			if (mobileMenu.classList.contains('active')) {
				closeMobileMenu();
			} else {
				openMobileMenu();
			}
		});

		// Fermer le menu mobile quand on clique sur l'overlay
		mobileMenuOverlay.addEventListener('click', closeMobileMenu);

		// Fermer le menu mobile quand on clique sur un lien
		mobileNavLinks.forEach(link => {
			link.addEventListener('click', closeMobileMenu);
		});

		// Fermer le menu mobile avec la touche Escape
		document.addEventListener('keydown', function(e) {
			if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
				closeMobileMenu();
			}
		});

		// Like system like Instagram
		function toggleLike(button, coachName) {
			const heart = button.querySelector('i');
			const countSpan = button.querySelector('.like-count');
			let currentCount = parseInt(countSpan.textContent);

			// Check if already liked (stored in localStorage)
			const likeKey = `coach_${coachName}_liked`;
			const isLiked = localStorage.getItem(likeKey) === 'true';

			if (isLiked) {
				// Unlike
				heart.classList.remove('text-red-500');
				heart.classList.add('text-white/70');
				button.classList.remove('liked');
				currentCount--;
				localStorage.setItem(likeKey, 'false');
			} else {
				// Like
				heart.classList.remove('text-white/70');
				heart.classList.add('text-red-500');
				button.classList.add('liked');
				currentCount++;
				localStorage.setItem(likeKey, 'true');

				// Create floating hearts effect
				createFloatingHearts(button);
			}

			countSpan.textContent = currentCount;

			// Store the new count
			localStorage.setItem(`coach_${coachName}_count`, currentCount);
		}

		// Create floating hearts animation
		function createFloatingHearts(button) {
			for (let i = 0; i < 5; i++) {
				setTimeout(() => {
					const heart = document.createElement('div');
					heart.innerHTML = '❤️';
					heart.style.position = 'absolute';
					heart.style.fontSize = '12px';
					heart.style.pointerEvents = 'none';
					heart.style.zIndex = '1000';
					heart.style.animation = 'floatUp 2s ease-out forwards';

					const rect = button.getBoundingClientRect();
					heart.style.left = (rect.left + Math.random() * 30) + 'px';
					heart.style.top = rect.top + 'px';

					document.body.appendChild(heart);

					setTimeout(() => {
						heart.remove();
					}, 2000);
				}, i * 100);
			}
		}

		// Add floating hearts animation
		const style = document.createElement('style');
		style.textContent = `
			@keyframes floatUp {
				0% { transform: translateY(0) scale(1); opacity: 1; }
				100% { transform: translateY(-100px) scale(0.5); opacity: 0; }
			}
		`;
		document.head.appendChild(style);

		// Load saved likes on page load
		document.addEventListener('DOMContentLoaded', function() {
			const coaches = ['yassine', 'samir', 'imane', 'ahmed', 'fatima', 'karim', 'laila'];

			coaches.forEach(coach => {
				const likeKey = `coach_${coach}_liked`;
				const countKey = `coach_${coach}_count`;
				const isLiked = localStorage.getItem(likeKey) === 'true';
				const savedCount = localStorage.getItem(countKey);

				const button = document.querySelector(`[onclick*="${coach}"]`);
				if (button) {
					const heart = button.querySelector('i');
					const countSpan = button.querySelector('.like-count');

					if (isLiked) {
						heart.classList.remove('text-white/70');
						heart.classList.add('text-red-500');
						button.classList.add('liked');
					}

					if (savedCount) {
						countSpan.textContent = savedCount;
					}
				}
			});
		});

		// Reveal on scroll
		const revealEls = Array.from(document.querySelectorAll('.reveal'));
		const observer = new IntersectionObserver((entries) => {
			entries.forEach(entry => {
				if (entry.isIntersecting) {
					entry.target.classList.add('visible');
					observer.unobserve(entry.target);
				}
			});
		}, { threshold: 0.15 });
		revealEls.forEach(el => observer.observe(el));

		// Smooth scroll for nav links
		document.querySelectorAll('a[href^="#"]').forEach(anchor => {
			anchor.addEventListener('click', function (e) {
				const href = this.getAttribute('href');
				if (!href || href === '#') return;
				e.preventDefault();
				document.querySelector(href)?.scrollIntoView({ behavior: 'smooth', block: 'start' });
			});
		});
	</script>

	<!-- Barre de Réseaux Sociaux Flottante -->
	<div class="social-media-bar fixed right-4 top-1/2 transform -translate-y-1/2 z-50" style="padding-right: 10px;">
		<div class="flex flex-col space-y-5">
			<!-- WhatsApp -->
			<a href="https://api.whatsapp.com/send?phone=+212709702820" target="_blank" class="whatsapp-button social-button" onclick="this.href=window.innerWidth > 768 ? 'https://web.whatsapp.com/send?phone=+212709702820' : 'https://api.whatsapp.com/send?phone=+212709702820';">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.890-5.335 11.893-11.893A11.821 11.821 0 0020.525 3.687"></path>
				</svg>
			</a>

			<!-- Waze -->
			<a class="waze-button social-button" href="https://waze.com/ul?ll=33.5731,-7.5898&navigate=yes" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
				</svg>
			</a>

			<!-- Facebook -->
			<a class="facebook-button social-button" href="https://www.facebook.com/powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"></path>
				</svg>
			</a>

			<!-- TikTok -->
			<a class="tiktok-button social-button" href="https://www.tiktok.com/@powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"></path>
				</svg>
			</a>

			<!-- Instagram -->
			<a class="instagram-button social-button" href="https://www.instagram.com/powerfitness/" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path>
				</svg>
			</a>
		</div>
	</div>

</body>
</html>
