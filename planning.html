<!DOCTYPE html>
<html lang="fr">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<title>Planning - Power Fitness</title>
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
	<script src="https://cdn.tailwindcss.com"></script>
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
	<link rel="icon" type="image/png" href="imgs/LOGO POWER FITNESS.png" />
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						'punch-red': '#CB2129',
						'punch-white': '#FFFFFF',
						'punch-black': '#000000',
					},
					fontFamily: {
						'punch': ['Roboto', 'system-ui', '-apple-system', 'Segoe UI', 'Ubuntu', 'Cantarell', 'Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
					},
					animation: {
						'fade-in': 'fadeIn 0.6s ease-out',
						'slide-up': 'slideUp 0.8s ease-out',
						'bounce-slow': 'bounce 2s infinite',
						'pulse-slow': 'pulse 3s infinite',
						'wiggle': 'wiggle 1s ease-in-out infinite',
						'slide-in': 'slideIn 0.5s ease-out',
					}
				}
			}
		}
	</script>
	<style>
		@keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes slideUp { from { opacity: 0; transform: translateY(40px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes slideIn { from { opacity: 0; transform: translateX(-20px); } to { opacity: 1; transform: translateX(0); } }
		@keyframes floatSlow { 0% { transform: translateY(0px); } 50% { transform: translateY(-10px); } 100% { transform: translateY(0px); } }
		@keyframes wiggle { 0%, 7% { transform: rotateZ(0); } 15% { transform: rotateZ(-15deg); } 20% { transform: rotateZ(10deg); } 25% { transform: rotateZ(-10deg); } 30% { transform: rotateZ(6deg); } 35% { transform: rotateZ(-4deg); } 40%, 100% { transform: rotateZ(0); } }
		@keyframes glow { 0% { box-shadow: 0 0 20px rgba(203, 33, 41, 0.3); } 50% { box-shadow: 0 0 40px rgba(203, 33, 41, 0.6); } 100% { box-shadow: 0 0 20px rgba(203, 33, 41, 0.3); } }

		body {
			background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
			background-attachment: fixed;
			position: relative;
			overflow-x: hidden;
			font-family: 'punch', system-ui, sans-serif;
		}

		body::before {
			content: '';
			position: fixed; inset: 0;
			background: radial-gradient(circle at 20% 20%, rgba(203, 33, 41, 0.15) 0%, transparent 50%),
			radial-gradient(circle at 80% 80%, rgba(203, 33, 41, 0.1) 0%, transparent 50%);
			pointer-events: none; z-index: 0;
		}

		.glass { backdrop-filter: blur(10px); background: rgba(255,255,255,0.12); border: 1px solid rgba(255,255,255,0.2); }
		.section-title { color: #ffffff; font-weight: 800; font-size: 1.875rem; line-height: 2.25rem; }
		@media (min-width: 768px) { .section-title { font-size: 2.25rem; line-height: 2.5rem; } }
		.section-subtitle { color: #d1d5db; }

		.reveal { opacity: 0; transform: translateY(20px); transition: opacity .6s ease, transform .6s ease; }
		.reveal.visible { opacity: 1; transform: translateY(0); }

		.schedule-card { transition: all 0.3s ease; }
		.schedule-card:hover { transform: scale(1.02); animation: glow 2s infinite; }
		
		.time-slot { transition: all 0.3s ease; }
		.time-slot:hover { background: rgba(203, 33, 41, 0.2); transform: translateY(-2px); }
		
		.course-badge { 
			background: linear-gradient(45deg, #CB2129, #ff4757); 
			animation: pulse 2s infinite;
		}
		
		.intensity-high { border-left: 4px solid #ef4444; }
		.intensity-medium { border-left: 4px solid #f97316; }
		.intensity-low { border-left: 4px solid #22c55e; }

		/* Barre de Réseaux Sociaux */
		.social-button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 45px;
			height: 45px;
			border-radius: 50%;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
			position: relative;
			overflow: hidden;
		}

		.whatsapp-button {
			background: #25D366;
		}

		.linkedin-button {
			background: #0077B5;
		}

		.facebook-button {
			background: #1877F2;
		}

		.tiktok-button {
			background: #000000;
		}

		.instagram-button {
			background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D, #F56040, #F77737, #FCAF45, #FFDC80);
		}

		.social-button:hover {
			transform: scale(1.1);
			box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
		}

		.social-button::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
			transition: left 0.5s;
		}

		.social-button:hover::before {
			left: 100%;
		}

		/* Animation d'apparition */
		.social-button {
			animation: slideInRight 0.6s ease-out forwards;
			opacity: 0;
			transform: translateX(100px);
		}

		.social-button:nth-child(1) { animation-delay: 0.1s; }
		.social-button:nth-child(2) { animation-delay: 0.2s; }
		.social-button:nth-child(3) { animation-delay: 0.3s; }
		.social-button:nth-child(4) { animation-delay: 0.4s; }
		.social-button:nth-child(5) { animation-delay: 0.5s; }

		@keyframes slideInRight {
			to {
				opacity: 1;
				transform: translateX(0);
			}
		}



		/* Menu Mobile - Sidebar Style */
		.mobile-menu {
			position: fixed;
			top: 0;
			left: 0;
			width: 220px;
			height: 100vh;
			background: rgba(0, 0, 0, 0.95);
			backdrop-filter: blur(15px);
			z-index: 50;
			transform: translateX(-100%);
			transition: transform 0.3s ease-in-out;
			border-right: 1px solid rgba(203, 33, 41, 0.3);
		}

		.mobile-menu.active {
			transform: translateX(0);
		}

		/* Overlay pour fermer le menu */
		.mobile-menu-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100vh;
			background: rgba(0, 0, 0, 0.5);
			z-index: 49;
			opacity: 0;
			visibility: hidden;
			transition: all 0.3s ease-in-out;
		}

		.mobile-menu-overlay.active {
			opacity: 1;
			visibility: visible;
		}

		.mobile-menu-content {
			display: flex;
			flex-direction: column;
			padding: 2rem 0;
			height: 100%;
		}

		.mobile-menu-header {
			padding: 0 1.5rem 1.5rem;
			border-bottom: 1px solid rgba(255, 255, 255, 0.1);
			margin-bottom: 1rem;
		}

		.mobile-menu-header img {
			width: 120px;
			height: auto;
		}

		.mobile-menu a {
			color: white;
			font-size: 1rem;
			font-weight: 500;
			text-decoration: none;
			padding: 0.875rem 1.5rem;
			transition: all 0.3s ease;
			border-left: 3px solid transparent;
			display: flex;
			align-items: center;
		}

		.mobile-menu a:hover,
		.mobile-menu a.text-punch-red {
			background: rgba(203, 33, 41, 0.1);
			border-left-color: #cb2129;
			color: #cb2129;
		}

		.mobile-menu a.bg-punch-red {
			background: #cb2129;
			color: white;
			margin: 1rem 1.5rem;
			border-radius: 0.5rem;
			border-left: none;
			justify-content: center;
			font-weight: 600;
		}

		.mobile-menu a:nth-child(1) { transition-delay: 0.1s; }
		.mobile-menu a:nth-child(2) { transition-delay: 0.2s; }
		.mobile-menu a:nth-child(3) { transition-delay: 0.3s; }
		.mobile-menu a:nth-child(4) { transition-delay: 0.4s; }
		.mobile-menu a:nth-child(5) { transition-delay: 0.5s; }
		.mobile-menu a:nth-child(6) { transition-delay: 0.6s; }

		.hamburger {
			display: none;
			flex-direction: column;
			cursor: pointer;
			padding: 0.5rem;
		}

		.hamburger span {
			width: 25px;
			height: 3px;
			background: white;
			margin: 3px 0;
			transition: 0.3s;
			border-radius: 2px;
		}

		.hamburger.active span:nth-child(1) {
			transform: rotate(-45deg) translate(-5px, 6px);
		}

		.hamburger.active span:nth-child(2) {
			opacity: 0;
		}

		.hamburger.active span:nth-child(3) {
			transform: rotate(45deg) translate(-5px, -6px);
		}

		.mobile-menu a:hover {
			color: #cb2129;
			background: rgba(203, 33, 41, 0.1);
		}

		.hamburger {
			display: none;
			flex-direction: column;
			cursor: pointer;
			padding: 0.5rem;
		}

		.hamburger span {
			width: 25px;
			height: 3px;
			background: white;
			margin: 3px 0;
			transition: 0.3s;
			border-radius: 2px;
		}

		.hamburger.active span:nth-child(1) {
			transform: rotate(-45deg) translate(-5px, 6px);
		}

		.hamburger.active span:nth-child(2) {
			opacity: 0;
		}

		.hamburger.active span:nth-child(3) {
			transform: rotate(45deg) translate(-5px, -6px);
		}

		/* Responsive */
		@media (max-width: 768px) {
			/* Menu hamburger visible sur mobile */
			.hamburger {
				display: flex;
			}

			/* Logo plus petit sur mobile */
			nav img {
				width: 100px !important;
			}
			/* Menu hamburger visible sur mobile */
			.hamburger {
				display: flex;
			}

			/* Logo plus petit sur mobile */
			nav img {
				width: 100px !important;
			}
			/* Barre de réseaux sociaux responsive */
			.social-media-bar {
				right: 8px !important;
				padding-right: 0 !important;
				top: 50% !important;
				transform: translateY(-50%) !important;
			}

			.social-media-bar .flex {
				gap: 12px !important;
			}

			.social-button {
				width: 38px;
				height: 38px;
			}
			.social-button svg {
				width: 24px;
				height: 24px;
			}
		}

		@media (max-width: 480px) {
			/* Logo encore plus petit sur très petits écrans */
			nav img {
				width: 80px !important;
			}

			/* Très petits écrans */
			.social-media-bar {
				right: 4px !important;
			}

			.social-media-bar .flex {
				gap: 8px !important;
			}

			.social-button {
				width: 32px;
				height: 32px;
			}
			.social-button svg {
				width: 20px;
				height: 20px;
			}
		}
	</style>
</head>
<body class="min-h-screen text-white">
	<!-- Hero Background -->
	<div class="min-h-screen" style="background-image: url('imgs/background.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat; background-attachment: fixed;">
		<!-- Navbar -->
		<nav class="bg-transparent px-6 py-4 absolute top-0 left-0 w-full z-20">
			<div class="max-w-7xl mx-auto flex items-center justify-between">
				<a href="index.html" class="flex items-center space-x-3">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" class="w-32 h-auto drop-shadow-[0_0_20px_rgba(203,33,41,0.3)]" />
				</a>

				<!-- Menu Desktop -->
				<div class="hidden md:flex items-center space-x-6">
					<a href="index.html" class="hover:text-punch-red nav-link">Accueil</a>
					<a href="abonnements.html" class="hover:text-punch-red nav-link">Abonnements</a>
					<a href="coach.html" class="hover:text-punch-red nav-link">Coachs</a>
					<a href="planning.html" class="text-punch-red nav-link">Planning</a>
					<a href="contact.html" class="hover:text-punch-red nav-link">Contact</a>
					<a href="register.php" class="px-4 py-2 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire</a>
				</div>

				<!-- Hamburger Menu Button -->
				<div class="hamburger md:hidden" id="hamburger">
					<span></span>
					<span></span>
					<span></span>
				</div>
			</div>
		</nav>

		<!-- Menu Mobile Overlay -->
		<div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

		<!-- Menu Mobile -->
		<div class="mobile-menu" id="mobileMenu">
			<div class="mobile-menu-content">
				<div class="mobile-menu-header">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" />
				</div>
				<a href="index.html" class="mobile-nav-link">Accueil</a>
				<a href="abonnements.html" class="mobile-nav-link">Abonnements</a>
				<a href="coach.html" class="mobile-nav-link">Coachs</a>
				<a href="planning.html" class="mobile-nav-link text-punch-red">Planning</a>
				<a href="contact.html" class="mobile-nav-link">Contact</a>
				<a href="register.php" class="mobile-nav-link bg-punch-red">S'inscrire</a>
			</div>
		</div>

		<!-- Hero Section -->
		<section class="max-w-5xl mx-auto px-6 pt-56 pb-24">
			<div class="flex flex-col items-center text-center gap-8">
				<div class="animate-fade-in">
					<h1 class="text-4xl md:text-6xl font-extrabold leading-tight">Planning des <span class="text-punch-red">Cours</span></h1>
					<p class="mt-5 text-gray-300 text-lg max-w-3xl mx-auto">Organisez votre semaine d'entraînement avec notre planning complet. Réservez vos créneaux préférés.</p>
				</div>
				<div class="glass rounded-xl p-6 border border-white/20 shadow-xl animate-slide-up">
					<div class="grid grid-cols-3 gap-6 text-center">
						<div><i class="fa-solid fa-calendar-days text-punch-red text-2xl mb-2 block animate-pulse"></i><span class="text-sm">7j/7</span></div>
						<div><i class="fa-solid fa-clock text-punch-red text-2xl mb-2 block animate-wiggle"></i><span class="text-sm">6h-22h</span></div>
						<div><i class="fa-solid fa-users text-punch-red text-2xl mb-2 block animate-bounce-slow"></i><span class="text-sm">35 cours/sem</span></div>
					</div>
				</div>
			</div>
		</section>
	</div>

	<!-- Quick Schedule Overview -->
	<!-- <section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Planning de la Semaine</h2>
				<p class="section-subtitle mt-4">Aperçu rapide de nos cours les plus populaires</p>
			</div>
			
			<div class="glass rounded-2xl p-8 border border-white/20 overflow-x-auto reveal">
				<table class="w-full text-left min-w-[800px]">
					<thead>
						<tr class="border-b border-white/20">
							<th class="py-4 px-4 text-gray-300 font-bold">Horaire</th>
							<th class="py-4 px-4 text-center font-bold">Lundi</th>
							<th class="py-4 px-4 text-center font-bold">Mardi</th>
							<th class="py-4 px-4 text-center font-bold">Mercredi</th>
							<th class="py-4 px-4 text-center font-bold">Jeudi</th>
							<th class="py-4 px-4 text-center font-bold">Vendredi</th>
							<th class="py-4 px-4 text-center font-bold">Samedi</th>
						</tr>
					</thead>
					<tbody class="text-sm">
						<tr class="border-b border-white/10 hover:bg-white/5 transition">
							<td class="py-4 px-4 font-semibold text-punch-red">06:00</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-high rounded-lg p-2">
									<div class="font-semibold">Cardio</div>
									<div class="text-xs text-gray-400">Yassine</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">—</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-medium rounded-lg p-2">
									<div class="font-semibold">HIIT</div>
									<div class="text-xs text-gray-400">Samir</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">—</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-low rounded-lg p-2">
									<div class="font-semibold">Yoga</div>
									<div class="text-xs text-gray-400">Imane</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-high rounded-lg p-2">
									<div class="font-semibold">Cross</div>
									<div class="text-xs text-gray-400">Yassine</div>
								</div>
							</td>
						</tr>
						<tr class="border-b border-white/10 hover:bg-white/5 transition">
							<td class="py-4 px-4 font-semibold text-punch-red">10:00</td>
							<td class="py-4 px-4 text-center">—</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-low rounded-lg p-2">
									<div class="font-semibold">Pilates</div>
									<div class="text-xs text-gray-400">Imane</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-high rounded-lg p-2">
									<div class="font-semibold">Cross</div>
									<div class="text-xs text-gray-400">Yassine</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-medium rounded-lg p-2">
									<div class="font-semibold">Zumba</div>
									<div class="text-xs text-gray-400">Samir</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">—</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-medium rounded-lg p-2">
									<div class="font-semibold">Body Pump</div>
									<div class="text-xs text-gray-400">Yassine</div>
								</div>
							</td>
						</tr>
						<tr class="border-b border-white/10 hover:bg-white/5 transition">
							<td class="py-4 px-4 font-semibold text-punch-red">18:00</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-medium rounded-lg p-2">
									<div class="font-semibold">Boxe</div>
									<div class="text-xs text-gray-400">Samir</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-high rounded-lg p-2">
									<div class="font-semibold">Cross</div>
									<div class="text-xs text-gray-400">Yassine</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-medium rounded-lg p-2">
									<div class="font-semibold">Spinning</div>
									<div class="text-xs text-gray-400">Samir</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-medium rounded-lg p-2">
									<div class="font-semibold">Boxe</div>
									<div class="text-xs text-gray-400">Samir</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-high rounded-lg p-2">
									<div class="font-semibold">Cross</div>
									<div class="text-xs text-gray-400">Yassine</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-low rounded-lg p-2">
									<div class="font-semibold">Stretching</div>
									<div class="text-xs text-gray-400">Imane</div>
								</div>
							</td>
						</tr>
						<tr class="hover:bg-white/5 transition">
							<td class="py-4 px-4 font-semibold text-punch-red">20:00</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-low rounded-lg p-2">
									<div class="font-semibold">Yoga</div>
									<div class="text-xs text-gray-400">Imane</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">—</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-medium rounded-lg p-2">
									<div class="font-semibold">HIIT</div>
									<div class="text-xs text-gray-400">Yassine</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-low rounded-lg p-2">
									<div class="font-semibold">Méditation</div>
									<div class="text-xs text-gray-400">Imane</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">
								<div class="time-slot intensity-low rounded-lg p-2">
									<div class="font-semibold">Yoga</div>
									<div class="text-xs text-gray-400">Imane</div>
								</div>
							</td>
							<td class="py-4 px-4 text-center">—</td>
						</tr>
					</tbody>
				</table>
			</div>
			
			<div class="mt-6 flex justify-center space-x-6 text-sm">
				<div class="flex items-center"><div class="w-4 h-4 bg-red-500 rounded mr-2"></div>Intensité Élevée</div>
				<div class="flex items-center"><div class="w-4 h-4 bg-orange-500 rounded mr-2"></div>Intensité Moyenne</div>
				<div class="flex items-center"><div class="w-4 h-4 bg-green-500 rounded mr-2"></div>Intensité Faible</div>
			</div>
		</div>
	</section> -->

	<!-- Daily Schedule Cards -->
	

	<!-- Plannings Spécialisés -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Plannings Spécialisés</h2>
				<p class="section-subtitle mt-4">Découvrez nos créneaux dédiés aux femmes et nos sessions mixtes</p>
			</div>

			<div class="grid lg:grid-cols-2 gap-8">
				<!-- Planning Femmes -->
				<div class="glass rounded-2xl overflow-hidden border border-white/20 reveal">
					<div class="relative">
						<img src="imgs/palaning femme.jpeg" alt="Planning Femmes" class="w-full h-64 object-cover">
						<div class="absolute top-4 left-4 bg-punch-red text-white px-4 py-2 rounded-full font-bold">
							<i class="fa-solid fa-venus mr-2"></i>100% FEMMES
						</div>
					</div>
					<div class="p-6">
						<h3 class="text-2xl font-bold mb-4 text-punch-red">Créneaux Femmes Exclusifs</h3>
						<p class="text-gray-300 mb-6">Des sessions dédiées exclusivement aux femmes dans un environnement bienveillant et motivant.</p>

						<div class="space-y-4">
							<div class="bg-punch-red/20 rounded-lg p-4 border-l-4 border-punch-red">
								<div class="flex justify-between items-center mb-2">
									<span class="font-semibold">Yoga Femmes</span>
									<span class="bg-punch-red text-white px-2 py-1 rounded-full text-xs">EXCLUSIF</span>
								</div>
								<div class="text-sm text-gray-300">Lundi & Mercredi • 09:00 - 10:00 • Imane</div>
								<div class="text-xs text-gray-400 mt-1">Espace privé • Max 15 femmes</div>
							</div>

							<div class="bg-punch-red/20 rounded-lg p-4 border-l-4 border-punch-red">
								<div class="flex justify-between items-center mb-2">
									<span class="font-semibold">Pilates Femmes</span>
									<span class="bg-punch-red text-white px-2 py-1 rounded-full text-xs">EXCLUSIF</span>
								</div>
								<div class="text-sm text-gray-300">Mardi & Jeudi • 10:30 - 11:30 • Imane</div>
								<div class="text-xs text-gray-400 mt-1">Espace privé • Max 12 femmes</div>
							</div>

							<div class="bg-punch-red/20 rounded-lg p-4 border-l-4 border-punch-red">
								<div class="flex justify-between items-center mb-2">
									<span class="font-semibold">Cardio Femmes</span>
									<span class="bg-punch-red text-white px-2 py-1 rounded-full text-xs">EXCLUSIF</span>
								</div>
								<div class="text-sm text-gray-300">Vendredi • 08:00 - 09:00 • Imane</div>
								<div class="text-xs text-gray-400 mt-1">Espace privé • Max 20 femmes</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Planning Mixte -->
				<div class="glass rounded-2xl overflow-hidden border border-white/20 reveal">
					<div class="relative">
						<img src="imgs/planningMixt.jpeg" alt="Planning Mixte" class="w-full h-64 object-cover">
						<div class="absolute top-4 left-4 bg-punch-red text-white px-4 py-2 rounded-full font-bold">
							<i class="fa-solid fa-users mr-2"></i>MIXTE
						</div>
					</div>
					<div class="p-6">
						<h3 class="text-2xl font-bold mb-4 text-punch-red">Sessions Mixtes</h3>
						<p class="text-gray-300 mb-6">Entraînez-vous ensemble dans nos cours ouverts à tous, hommes et femmes.</p>

						<div class="space-y-4">
							<div class="bg-punch-red/20 rounded-lg p-4 border-l-4 border-punch-red">
								<div class="flex justify-between items-center mb-2">
									<span class="font-semibold">Cross Training Mixte</span>
									<span class="bg-punch-red text-white px-2 py-1 rounded-full text-xs">INTENSE</span>
								</div>
								<div class="text-sm text-gray-300">Lundi, Mercredi, Vendredi • 18:00 - 18:45 • Yassine</div>
								<div class="text-xs text-gray-400 mt-1">Tous niveaux • Max 15 personnes</div>
							</div>

							<div class="bg-punch-red/20 rounded-lg p-4 border-l-4 border-punch-red">
								<div class="flex justify-between items-center mb-2">
									<span class="font-semibold">Boxe Mixte</span>
									<span class="bg-punch-red text-white px-2 py-1 rounded-full text-xs">MOYEN</span>
								</div>
								<div class="text-sm text-gray-300">Mardi & Jeudi • 19:00 - 19:50 • Samir</div>
								<div class="text-xs text-gray-400 mt-1">Débutants bienvenus • Max 12 personnes</div>
							</div>

							<div class="bg-punch-red/20 rounded-lg p-4 border-l-4 border-punch-red">
								<div class="flex justify-between items-center mb-2">
									<span class="font-semibold">HIIT Mixte</span>
									<span class="bg-punch-red text-white px-2 py-1 rounded-full text-xs">INTENSE</span>
								</div>
								<div class="text-sm text-gray-300">Samedi • 07:00 - 07:30 • Yassine</div>
								<div class="text-xs text-gray-400 mt-1">Haute intensité • Max 18 personnes</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Nos Espaces d'Entraînement -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Nos Espaces d'Entraînement</h2>
				<p class="section-subtitle mt-4">Découvrez nos équipements et ambiance</p>
			</div>

			<div class="grid lg:grid-cols-2 gap-8">
				<!-- Première Grande Image avec Animation -->
				<div class="glass rounded-2xl overflow-hidden border border-white/20 reveal" style="animation: floatSlow 6s ease-in-out infinite;">
					<div class="relative group">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (1).jpeg" alt="Espace Principal" class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
						<div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
						<div class="absolute bottom-6 left-6 right-6">
							<h3 class="text-2xl font-bold text-white mb-2">Espace Principal</h3>
							<p class="text-gray-200 text-sm">Notre salle principale équipée des dernières technologies</p>
						</div>
						<div class="absolute top-4 right-4 bg-punch-red text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
							NOUVEAU
						</div>
					</div>
				</div>

				<!-- Deuxième Grande Image -->
				<div class="glass rounded-2xl overflow-hidden border border-white/20 reveal" style="animation: floatSlow 6s 1s ease-in-out infinite;">
					<div class="relative group">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.21 (2).jpeg" alt="Zone Spécialisée" class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-110">
						<div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
						<div class="absolute bottom-6 left-6 right-6">
							<h3 class="text-2xl font-bold text-white mb-2">Zone Spécialisée</h3>
							<p class="text-gray-200 text-sm">Espace dédié aux entraînements spécialisés et cours</p>
						</div>
						<div class="absolute top-4 right-4 bg-punch-red text-white px-3 py-1 rounded-full text-xs font-bold">
							PREMIUM
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Horaires Détaillés -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Horaires Complets</h2>
				<p class="section-subtitle mt-4">Planning détaillé de tous nos créneaux femmes et mixtes</p>
			</div>

			<div class="grid lg:grid-cols-2 gap-8">
				<!-- Horaires Femmes -->
				<div class="glass rounded-2xl p-8 border border-white/20 reveal">
					<div class="flex items-center mb-6">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (6).jpeg" alt="Cours Femmes" class="w-16 h-16 rounded-full object-cover mr-4 border-2 border-punch-red">
						<div>
							<h3 class="text-xl font-bold text-punch-red">Planning Femmes</h3>
							<p class="text-gray-300 text-sm">Espace privé et sécurisé</p>
						</div>
					</div>

					<div class="space-y-4">
						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Lundi</h4>
							<div class="text-sm text-gray-300">09:00 - 10:00 • Yoga Femmes • Imane</div>
							<div class="text-sm text-gray-300">14:00 - 15:00 • Pilates Doux • Imane</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Mardi</h4>
							<div class="text-sm text-gray-300">10:30 - 11:30 • Pilates Femmes • Imane</div>
							<div class="text-sm text-gray-300">16:00 - 17:00 • Cardio Doux • Imane</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Mercredi</h4>
							<div class="text-sm text-gray-300">09:00 - 10:00 • Yoga Femmes • Imane</div>
							<div class="text-sm text-gray-300">15:30 - 16:30 • Stretching • Imane</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Jeudi</h4>
							<div class="text-sm text-gray-300">10:30 - 11:30 • Pilates Femmes • Imane</div>
							<div class="text-sm text-gray-300">17:00 - 18:00 • Méditation • Imane</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Vendredi</h4>
							<div class="text-sm text-gray-300">08:00 - 09:00 • Cardio Femmes • Imane</div>
							<div class="text-sm text-gray-300">14:30 - 15:30 • Yoga Détente • Imane</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Samedi</h4>
							<div class="text-sm text-gray-300">09:30 - 10:30 • Pilates Matinal • Imane</div>
							<div class="text-sm text-gray-300">11:00 - 12:00 • Yoga Flow • Imane</div>
						</div>
					</div>
				</div>

				<!-- Horaires Mixtes -->
				<div class="glass rounded-2xl p-8 border border-white/20 reveal">
					<div class="flex items-center mb-6">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.20 (2).jpeg" alt="Cours Mixtes" class="w-16 h-16 rounded-full object-cover mr-4 border-2 border-punch-red">
						<div>
							<h3 class="text-xl font-bold text-punch-red">Planning Mixte</h3>
							<p class="text-gray-300 text-sm">Ouvert à tous</p>
						</div>
					</div>

					<div class="space-y-4">
						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Lundi</h4>
							<div class="text-sm text-gray-300">06:00 - 06:45 • Cardio Matinal • Yassine</div>
							<div class="text-sm text-gray-300">18:00 - 18:45 • Cross Training • Yassine</div>
							<div class="text-sm text-gray-300">19:00 - 19:50 • Boxe • Samir</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Mardi</h4>
							<div class="text-sm text-gray-300">18:00 - 18:45 • Cross Training • Yassine</div>
							<div class="text-sm text-gray-300">19:30 - 20:20 • HIIT • Samir</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Mercredi</h4>
							<div class="text-sm text-gray-300">06:00 - 06:30 • HIIT Matinal • Samir</div>
							<div class="text-sm text-gray-300">18:00 - 18:45 • Cross Training • Yassine</div>
							<div class="text-sm text-gray-300">20:00 - 20:30 • HIIT • Yassine</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Jeudi</h4>
							<div class="text-sm text-gray-300">10:00 - 10:50 • Zumba • Samir</div>
							<div class="text-sm text-gray-300">18:00 - 18:50 • Boxe • Samir</div>
							<div class="text-sm text-gray-300">19:30 - 20:15 • Body Pump • Yassine</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Vendredi</h4>
							<div class="text-sm text-gray-300">18:00 - 18:45 • Cross Training • Yassine</div>
							<div class="text-sm text-gray-300">19:00 - 19:45 • Spinning • Samir</div>
						</div>

						<div class="border-l-4 border-punch-red pl-4 py-2">
							<h4 class="font-semibold text-punch-red">Samedi</h4>
							<div class="text-sm text-gray-300">06:00 - 06:45 • Cross Training • Yassine</div>
							<div class="text-sm text-gray-300">07:00 - 07:30 • HIIT Intense • Yassine</div>
							<div class="text-sm text-gray-300">10:00 - 10:55 • Body Pump • Yassine</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Équipements et Installations -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Nos Équipements</h2>
				<p class="section-subtitle mt-4">Des installations modernes pour tous vos entraînements</p>
			</div>

			<div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.20 (3).jpeg" alt="Équipement Cardio" class="w-full h-32 object-cover rounded-lg mb-4">
					<h3 class="font-bold mb-2">Zone Cardio</h3>
					<p class="text-gray-300 text-sm">Tapis de course, vélos elliptiques, rameurs dernière génération</p>
					<div class="mt-3 text-xs text-punch-red">
						<i class="fa-solid fa-heart-pulse mr-1"></i>15 machines
					</div>
				</div>

				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.20 (4).jpeg" alt="Espace Musculation" class="w-full h-32 object-cover rounded-lg mb-4">
					<h3 class="font-bold mb-2">Musculation</h3>
					<p class="text-gray-300 text-sm">Haltères, barres, machines guidées et charges libres</p>
					<div class="mt-3 text-xs text-punch-red">
						<i class="fa-solid fa-dumbbell mr-1"></i>200m² d'espace
					</div>
				</div>

				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.21 (1).jpeg" alt="Studio Cours" class="w-full h-32 object-cover rounded-lg mb-4">
					<h3 class="font-bold mb-2">Studio Privé</h3>
					<p class="text-gray-300 text-sm">Espace dédié aux cours femmes avec vestiaires séparés</p>
					<div class="mt-3 text-xs text-punch-red">
						<i class="fa-solid fa-venus mr-1"></i>Accès sécurisé
					</div>
				</div>

				<div class="glass rounded-xl p-6 border border-white/20 text-center reveal">
					<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.21 (2).jpeg" alt="Espace Cross" class="w-full h-32 object-cover rounded-lg mb-4">
					<h3 class="font-bold mb-2">Cross Training</h3>
					<p class="text-gray-300 text-sm">Kettlebells, cordes, pneus, TRX et équipements fonctionnels</p>
					<div class="mt-3 text-xs text-punch-red">
						<i class="fa-solid fa-fire mr-1"></i>Zone dédiée
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Témoignages Planning -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Ce que disent nos membres</h2>
				<p class="section-subtitle mt-4">Retours d'expérience sur nos plannings spécialisés</p>
			</div>

			<div class="grid md:grid-cols-3 gap-8">
				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<div class="flex items-center mb-4">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (1).jpeg" alt="Membre Fatima" class="w-12 h-12 rounded-full object-cover mr-4 border-2 border-punch-red">
						<div>
							<h4 class="font-bold">Fatima</h4>
							<div class="text-xs text-punch-red">Cours Femmes</div>
						</div>
					</div>
					<p class="text-gray-300 text-sm">"L'espace femmes est parfait ! Je me sens à l'aise et motivée. Les cours d'Imane sont excellents."</p>
					<div class="mt-3 flex text-yellow-400">
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
					</div>
				</div>

				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<div class="flex items-center mb-4">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (2).jpeg" alt="Membre Karim" class="w-12 h-12 rounded-full object-cover mr-4 border-2 border-punch-red">
						<div>
							<h4 class="font-bold">Karim</h4>
							<div class="text-xs text-punch-red">Cours Mixtes</div>
						</div>
					</div>
					<p class="text-gray-300 text-sm">"Les cours de cross training avec Yassine sont intenses ! L'ambiance mixte est très motivante."</p>
					<div class="mt-3 flex text-yellow-400">
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
					</div>
				</div>

				<div class="glass rounded-xl p-6 border border-white/20 reveal">
					<div class="flex items-center mb-4">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (3).jpeg" alt="Membre Aicha" class="w-12 h-12 rounded-full object-cover mr-4 border-2 border-punch-red">
						<div>
							<h4 class="font-bold">Aicha</h4>
							<div class="text-xs text-punch-red">Cours Femmes</div>
						</div>
					</div>
					<p class="text-gray-300 text-sm">"Enfin un planning adapté ! Les horaires femmes me permettent de concilier travail et sport."</p>
					<div class="mt-3 flex text-yellow-400">
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
						<i class="fa-solid fa-star"></i>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Avantages Planning -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="glass rounded-2xl p-8 border border-white/20 reveal">
				<div class="text-center mb-12">
					<h2 class="section-title">Pourquoi Choisir Nos Plannings ?</h2>
					<p class="section-subtitle mt-4">Des créneaux pensés pour tous</p>
				</div>

				<div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
					<div class="text-center">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (4).jpeg" alt="Flexibilité" class="w-20 h-20 mx-auto rounded-full object-cover mb-4 border-2 border-punch-red">
						<h3 class="font-bold mb-2">Flexibilité</h3>
						<p class="text-gray-300 text-sm">Horaires adaptés à votre emploi du temps</p>
					</div>

					<div class="text-center">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (5).jpeg" alt="Sécurité" class="w-20 h-20 mx-auto rounded-full object-cover mb-4 border-2 border-punch-red">
						<h3 class="font-bold mb-2">Espace Sécurisé</h3>
						<p class="text-gray-300 text-sm">Zone femmes privée et protégée</p>
					</div>

					<div class="text-center">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (6).jpeg" alt="Qualité" class="w-20 h-20 mx-auto rounded-full object-cover mb-4 border-2 border-punch-red">
						<h3 class="font-bold mb-2">Coachs Experts</h3>
						<p class="text-gray-300 text-sm">Encadrement professionnel et bienveillant</p>
					</div>

					<div class="text-center">
						<img src="imgs/WhatsApp Image 2025-08-22 at 14.34.22 (7).jpeg" alt="Communauté" class="w-20 h-20 mx-auto rounded-full object-cover mb-4 border-2 border-green-500">
						<h3 class="font-bold mb-2">Communauté</h3>
						<p class="text-gray-300 text-sm">Ambiance conviviale et motivante</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Booking Info -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="text-center mb-16 reveal">
				<h2 class="section-title">Comment Réserver ?</h2>
				<p class="section-subtitle mt-4">Réservez facilement vos cours préférés</p>
			</div>

			<div class="grid md:grid-cols-3 gap-8">
				<div class="glass rounded-2xl p-8 border border-white/20 text-center reveal">
					<div class="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-punch-red to-red-700 flex items-center justify-center mb-6">
						<i class="fa-solid fa-user-plus text-2xl text-white"></i>
					</div>
					<h3 class="text-xl font-bold mb-4">1. Inscrivez-vous</h3>
					<p class="text-gray-300">Choisissez votre abonnement et créez votre compte membre</p>
				</div>

				<div class="glass rounded-2xl p-8 border border-white/20 text-center reveal">
					<div class="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-punch-red to-red-700 flex items-center justify-center mb-6">
						<i class="fa-solid fa-calendar-plus text-2xl text-white"></i>
					</div>
					<h3 class="text-xl font-bold mb-4">2. Réservez</h3>
					<p class="text-gray-300">Sélectionnez vos cours dans notre planning en ligne ou à l'accueil</p>
				</div>

				<div class="glass rounded-2xl p-8 border border-white/20 text-center reveal">
					<div class="w-20 h-20 mx-auto rounded-full bg-gradient-to-br from-punch-red to-red-700 flex items-center justify-center mb-6">
						<i class="fa-solid fa-dumbbell text-2xl text-white"></i>
					</div>
					<h3 class="text-xl font-bold mb-4">3. Entraînez-vous</h3>
					<p class="text-gray-300">Venez 10 minutes avant le cours et profitez de votre séance</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Rules & Info -->
	<section class="py-20">
		<div class="max-w-4xl mx-auto px-6">
			<div class="glass rounded-2xl p-8 border border-white/20 reveal">
				<h2 class="section-title text-center mb-8">Informations Importantes</h2>

				<div class="grid md:grid-cols-2 gap-8">
					<div>
						<h3 class="text-lg font-bold mb-4 text-punch-red">Règles de Réservation</h3>
						<ul class="space-y-3 text-gray-300">
							<li class="flex items-start"><i class="fa-solid fa-check text-punch-red mr-3 mt-1"></i>Réservation obligatoire pour tous les cours</li>
							<li class="flex items-start"><i class="fa-solid fa-check text-punch-red mr-3 mt-1"></i>Annulation possible jusqu'à 2h avant</li>
							<li class="flex items-start"><i class="fa-solid fa-check text-punch-red mr-3 mt-1"></i>Arrivée 10 minutes avant le début</li>
							<li class="flex items-start"><i class="fa-solid fa-check text-punch-red mr-3 mt-1"></i>Tenue de sport obligatoire</li>
						</ul>
					</div>

					<div>
						<h3 class="text-lg font-bold mb-4 text-punch-red">Équipements Fournis</h3>
						<ul class="space-y-3 text-gray-300">
							<li class="flex items-start"><i class="fa-solid fa-check text-punch-red mr-3 mt-1"></i>Tapis de yoga et pilates</li>
							<li class="flex items-start"><i class="fa-solid fa-check text-punch-red mr-3 mt-1"></i>Gants de boxe et protections</li>
							<li class="flex items-start"><i class="fa-solid fa-check text-punch-red mr-3 mt-1"></i>Poids et élastiques</li>
							<li class="flex items-start"><i class="fa-solid fa-check text-punch-red mr-3 mt-1"></i>Serviettes désinfectantes</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="py-20">
		<div class="max-w-5xl mx-auto px-6 text-center glass rounded-2xl p-10 border border-white/20 reveal">
			<h2 class="text-3xl md:text-4xl font-extrabold mb-4">Prêt à Commencer ?</h2>
			<p class="text-gray-300 mb-8">Rejoignez nos cours dès aujourd'hui et transformez votre routine fitness</p>
			<div class="flex flex-wrap justify-center gap-4">
				<a href="register.php" class="px-8 py-4 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire maintenant</a>
				<a href="cours.html" class="px-8 py-4 glass border border-white/20 rounded-lg font-semibold hover:bg-white/20 transition">Découvrir nos cours</a>
			</div>
		</div>
	</section>

	<!-- Footer -->
	<footer class="py-12 px-6" style="background-color: #000000;">
		<div class="container mx-auto max-w-6xl">
			<div class="grid md:grid-cols-4 gap-8 mb-8">
				<div class="md:col-span-1">
					<div class="flex items-center mb-4">
						<span class="text-2xl font-bold text-white">POWER</span>
						<span class="text-2xl font-bold text-brand-red">FITNESS</span>
					</div>
					<p class="text-gray-400 mb-4">Transform your limits. Unleash your power.</p>
					<div class="flex space-x-4">
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
							</svg>
						</a>
					</div>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Programs</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Strength Training</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Cardio Power</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Functional Fitness</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">HIIT Sessions</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Facility</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Equipment</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Amenities</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Hours</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Location</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Contact</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Join Now</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Book Tour</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Support</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Careers</a></li>
					</ul>
				</div>
			</div>

			<div class="border-t border-gray-800 pt-8 text-center">
				<p class="text-gray-400">&copy; 2025 Power Fitness. All rights reserved.</p>
			</div>
		</div>
	</footer>

	

	<!-- Scripts -->
	<script>
		// Menu Mobile Toggle
		const hamburger = document.getElementById('hamburger');
		const mobileMenu = document.getElementById('mobileMenu');
		const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
		const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

		function openMobileMenu() {
			hamburger.classList.add('active');
			mobileMenu.classList.add('active');
			mobileMenuOverlay.classList.add('active');
			document.body.style.overflow = 'hidden';
		}

		function closeMobileMenu() {
			hamburger.classList.remove('active');
			mobileMenu.classList.remove('active');
			mobileMenuOverlay.classList.remove('active');
			document.body.style.overflow = '';
		}

		hamburger.addEventListener('click', function() {
			if (mobileMenu.classList.contains('active')) {
				closeMobileMenu();
			} else {
				openMobileMenu();
			}
		});

		// Fermer le menu mobile quand on clique sur l'overlay
		mobileMenuOverlay.addEventListener('click', closeMobileMenu);

		// Fermer le menu mobile quand on clique sur un lien
		mobileNavLinks.forEach(link => {
			link.addEventListener('click', closeMobileMenu);
		});

		// Fermer le menu mobile avec la touche Escape
		document.addEventListener('keydown', function(e) {
			if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
				closeMobileMenu();
			}
		});

		// Reveal on scroll
		const revealEls = Array.from(document.querySelectorAll('.reveal'));
		const observer = new IntersectionObserver((entries) => {
			entries.forEach(entry => {
				if (entry.isIntersecting) {
					entry.target.classList.add('visible');
					observer.unobserve(entry.target);
				}
			});
		}, { threshold: 0.15 });
		revealEls.forEach(el => observer.observe(el));

		// Smooth scroll for nav links
		document.querySelectorAll('a[href^="#"]').forEach(anchor => {
			anchor.addEventListener('click', function (e) {
				const href = this.getAttribute('href');
				if (!href || href === '#') return;
				e.preventDefault();
				document.querySelector(href)?.scrollIntoView({ behavior: 'smooth', block: 'start' });
			});
		});

		// Interactive schedule hover effects
		document.querySelectorAll('.time-slot').forEach(slot => {
			slot.addEventListener('mouseenter', function() {
				this.style.transform = 'scale(1.05)';
			});
			slot.addEventListener('mouseleave', function() {
				this.style.transform = 'scale(1)';
			});
		});
	</script>

	<!-- Barre de Réseaux Sociaux Flottante -->
	<div class="social-media-bar fixed right-4 top-1/2 transform -translate-y-1/2 z-50" style="padding-right: 10px;">
		<div class="flex flex-col space-y-5">
			<!-- WhatsApp -->
			<a href="https://api.whatsapp.com/send?phone=+212709702820" target="_blank" class="whatsapp-button social-button" onclick="this.href=window.innerWidth > 768 ? 'https://web.whatsapp.com/send?phone=+212709702820' : 'https://api.whatsapp.com/send?phone=+212709702820';">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.890-5.335 11.893-11.893A11.821 11.821 0 0020.525 3.687"></path>
				</svg>
			</a>

			<!-- LinkedIn -->
			<a class="linkedin-button social-button" href="https://www.linkedin.com/company/powerfitness/" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path>
				</svg>
			</a>

			<!-- Facebook -->
			<a class="facebook-button social-button" href="https://www.facebook.com/powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"></path>
				</svg>
			</a>

			<!-- TikTok -->
			<a class="tiktok-button social-button" href="https://www.tiktok.com/@powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"></path>
				</svg>
			</a>

			<!-- Instagram -->
			<a class="instagram-button social-button" href="https://www.instagram.com/powerfitness/" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path>
				</svg>
			</a>
		</div>
	</div>
</body>
</html>
