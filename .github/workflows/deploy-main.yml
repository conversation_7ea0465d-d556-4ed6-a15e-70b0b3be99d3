on:
  push:
    branches:
      - main
name: 🚀 Deploy POWER WEBSITE
jobs:
  web-deploy:
    name: 🎉 Deploy POWER WEBSITE
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v2
    
    - name: 📂 Sync files TO SERVER
      uses: SamKirkland/FTP-Deploy-Action@4.0.0
      with:
        server: ${{ vars.FTP_HOST }}
        username: ${{ vars.FTP_USERNAME_WEB }}
        password: ${{ secrets.FTP_PASS_WEB }}
        exclude: .git*
          - .git*/**
          - node_modules/**
          - node_modules/**/*
          - .ENV
          - .DS_Store
          - error_log