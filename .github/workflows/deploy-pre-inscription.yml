on:
  push:
    branches:
      - pre-inscription
name: 🚀 Deploy PRE INSCRIPTION
jobs:
  web-deploy:
    name: 🎉 Deploy PRE INSCRIPTION
    runs-on: ubuntu-latest
    steps:
    - name: 🚚 Get latest code
      uses: actions/checkout@v2
    
    - name: 📂 Sync files TO SERVER
      uses: SamKirkland/FTP-Deploy-Action@4.0.0
      with:
        server: ${{ vars.FTP_HOST }}
        username: ${{ vars.FTP_USERNAME_PI }}
        password: ${{ secrets.FTP_PASS_PI }}
        exclude: .git*
          - .git*/**
          - node_modules/**
          - node_modules/**/*
          - .ENV
          - .DS_Store
          - error_log