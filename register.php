<?php
	declare(strict_types=1);
	session_start();

	// Configuration
	$databaseFilePath = __DIR__ . '/database.sqlite';
	$dsn = 'sqlite:' . $databaseFilePath;

	// Initialize database and table
	$pdo = null;
	$databaseError = null;
	try {
		$pdo = new PDO($dsn);
		$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
		$pdo->exec('PRAGMA foreign_keys = ON');
		$pdo->exec(
			'CREATE TABLE IF NOT EXISTS users (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				name TEXT NOT NULL,
				firstname TEXT NOT NULL,
				email TEXT NOT NULL UNIQUE,
				phone TEXT NOT NULL,
				civility TEXT NOT NULL,
				birthdate TEXT NOT NULL,
				cin TEXT NOT NULL,
				city TEXT NOT NULL,
				created_at TEXT NOT NULL
			)'
		);
	} catch (Throwable $e) {
		$databaseError = 'Database error: ' . $e->getMessage();
	}

	// Handle flash messages
	$flashSuccess = $_SESSION['flash_success'] ?? null;
	$flashError = $_SESSION['flash_error'] ?? null;
	unset($_SESSION['flash_success'], $_SESSION['flash_error']);

	$errors = [];
	$fieldErrors = [];
	$prefill = [
		'name' => '',
		'firstname' => '',
		'email' => '',
		'phone' => '',
		'birthdate' => '',
		'civility' => '',
		'cin' => '',
		'city' => ''
	];

	// Forcer la réinitialisation lors d'une requête GET (refresh)
	if ($_SERVER['REQUEST_METHOD'] === 'GET') {
		$fieldErrors = [];
		$errors = [];
	}

	if ($_SERVER['REQUEST_METHOD'] === 'POST' && $databaseError === null && $pdo instanceof PDO) {
		$name = isset($_POST['name']) ? trim((string)$_POST['name']) : '';
		$firstname = isset($_POST['firstname']) ? trim((string)$_POST['firstname']) : '';
		$email = isset($_POST['email']) ? trim((string)$_POST['email']) : '';
		$phone = isset($_POST['phone']) ? trim((string)$_POST['phone']) : '';
		$birthdate = isset($_POST['birthdate']) ? trim((string)$_POST['birthdate']) : '';
		$civility = isset($_POST['civility']) ? trim((string)$_POST['civility']) : '';
		$cin = isset($_POST['cin']) ? trim((string)$_POST['cin']) : '';
		$city = isset($_POST['city']) ? trim((string)$_POST['city']) : '';

		$prefill['name'] = $name;
		$prefill['firstname'] = $firstname;
		$prefill['email'] = $email;
		$prefill['phone'] = $phone;
		$prefill['birthdate'] = $birthdate;
		$prefill['civility'] = $civility;
		$prefill['cin'] = $cin;
		$prefill['city'] = $city;

		// Validation des champs requis
		$fieldErrors = [];
		if ($name === '') {
			$fieldErrors['name'] = 'Le nom est requis.';
		}
		if ($firstname === '') {
			$fieldErrors['firstname'] = 'Le prénom est requis.';
		}
		if ($email === '' || filter_var($email, FILTER_VALIDATE_EMAIL) === false) {
			$fieldErrors['email'] = 'Une adresse email valide est requise.';
		}
		if ($phone === '') {
			$fieldErrors['phone'] = 'Le téléphone est requis.';
		}
		if ($civility === '') {
			$fieldErrors['civility'] = 'La civilité est requise.';
		}
		if ($birthdate === '') {
			$fieldErrors['birthdate'] = 'La date de naissance est requise.';
		}
		if ($cin === '') {
			$fieldErrors['cin'] = 'Le CIN est requis.';
		}
		if ($city === '') {
			$fieldErrors['city'] = 'La ville est requise.';
		}

		// Ajouter les erreurs de champs au tableau global pour la logique existante
		$errors = array_values($fieldErrors);

		if (empty($errors)) {
			try {
				$insert = $pdo->prepare('INSERT INTO users (name, firstname, email, phone, civility, birthdate, cin, city, created_at) VALUES (:name, :firstname, :email, :phone, :civility, :birthdate, :cin, :city, :created_at)');
				$insert->execute([
					':name' => $name,
					':firstname' => $firstname,
					':email' => strtolower($email),
					':phone' => $phone,
					':civility' => $civility,
					':birthdate' => $birthdate,
					':cin' => $cin,
					':city' => $city,
					':created_at' => (new DateTimeImmutable('now'))->format(DateTimeInterface::ATOM),
				]);

				$_SESSION['flash_success'] = 'Inscription réussie. Bienvenue chez Power Fitness !';
				header('Location: ' . $_SERVER['PHP_SELF']);
				exit;
			} catch (PDOException $e) {
				if ($e->getCode() === '23000') { // constraint violation (likely unique email)
					$errors[] = 'Cette adresse email est déjà enregistrée.';
				} else {
					$errors[] = 'Échec de l\'inscription. Veuillez réessayer.';
				}
			} catch (Throwable $e) {
				$errors[] = 'Erreur inattendue. Veuillez réessayer.';
			}
		}
	}

	function e(string $value): string {
		return htmlspecialchars($value, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8');
	}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Inscription - Power Fitness</title>
<link rel="icon" type="image/png" href="imgs/LOGO POWER FITNESS.png" />
<link rel="icon" type="image/png" sizes="32x32" href="imgs/LOGO POWER FITNESS.png" />
<link rel="icon" type="image/png" sizes="16x16" href="imgs/LOGO POWER FITNESS.png" />
<link rel="apple-touch-icon" href="imgs/LOGO POWER FITNESS.png" />
<script src="https://cdn.tailwindcss.com"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<link rel='icon' href='./assets/images/logo.png' type='image/x-icon'>
<!-- Flatpickr - Simple White Calendar -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/fr.js"></script>
<script>
tailwind.config = {
theme: {
extend: {
colors: {
'punch-red': '#CB2129',
'punch-white': '#FFFFFF',
'punch-black': '#000000',
},
fontFamily: {
'punch': ['system-ui', '-apple-system', 'Segoe UI', 'Roboto', 'Ubuntu', 'Cantarell', 'Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
},
animation: {
'fade-in': 'fadeIn 0.6s ease-out',
'slide-up': 'slideUp 0.8s ease-out',
'pulse-red': 'pulseRed 2s infinite',
}
}
}
}
</script>
<style>
@keyframes fadeIn {
from { opacity: 0; transform: translateY(20px); }
to { opacity: 1; transform: translateY(0); }
}
@keyframes slideUp {
from { opacity: 0; transform: translateY(40px); }
to { opacity: 1; transform: translateY(0); }
}
@keyframes pulseRed {
0%, 100% { box-shadow: 0 0 0 0 rgba(203, 33, 41, 0.7); }
70% { box-shadow: 0 0 0 10px rgba(203, 33, 41, 0); }
}
@keyframes float {
0%, 100% { transform: translateY(0px) rotate(0deg); }
50% { transform: translateY(-20px) rotate(180deg); }
}

body {
background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
background-attachment: fixed;
position: relative;
overflow-x: hidden;
font-family: 'punch', system-ui, sans-serif;
}

body::before {
content: '';
position: fixed;
top: 0;
left: 0;
right: 0;
bottom: 0;
background: radial-gradient(circle at 20% 20%, rgba(203, 33, 41, 0.15) 0%, transparent 50%),
radial-gradient(circle at 80% 80%, rgba(203, 33, 41, 0.1) 0%, transparent 50%);
pointer-events: none;
z-index: 0;
}

.glass-effect {
backdrop-filter: blur(20px);
background: rgba(255, 255, 255, 0.95);
border: 1px solid rgba(255, 255, 255, 0.2);
}

.input-glow:focus {
box-shadow: 0 0 0 3px rgba(203, 33, 41, 0.1), 0 0 20px rgba(203, 33, 41, 0.2);
}

.btn-punch {
background: linear-gradient(135deg, #CB2129 0%, #a01e24 100%);
position: relative;
overflow: hidden;
}

.btn-punch::before {
content: '';
position: absolute;
top: 0;
left: -100%;
width: 100%;
height: 100%;
background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
transition: left 0.5s;
}

.btn-punch:hover::before {
left: 100%;
}

.floating-shapes {
position: absolute;
width: 100%;
height: 100%;
overflow: hidden;
z-index: 1;
}

.shape {
position: absolute;
background: rgba(203, 33, 41, 0.1);
border-radius: 50%;
animation: float 6s ease-in-out infinite;
}

.shape:nth-child(1) {
width: 80px;
height: 80px;
top: 10%;
left: 10%;
animation-delay: 0s;
}

.shape:nth-child(2) {
width: 120px;
height: 120px;
top: 70%;
right: 10%;
animation-delay: 2s;
}

.shape:nth-child(3) {
width: 60px;
height: 60px;
top: 40%;
left: 80%;
animation-delay: 4s;
}

.logo-glow {
filter: drop-shadow(0 0 20px rgba(203, 33, 41, 0.3));
}

/* Animation pour les inputs */
.input-container {
position: relative;
margin-bottom: 2rem;
}

.input-container label {
position: absolute;
top: 0;
left: 0;
font-size: 0.875rem;
font-weight: 500;
color: #9ca3af;
transition: all 0.3s ease;
pointer-events: none;
transform-origin: left top;
z-index: 1;
}

.input-container input,
.input-container select {
width: 100%;
padding: 1rem 0 0.5rem 0;
border: none;
border-bottom: 2px solid #d1d5db;
background: transparent;
outline: none;
font-size: 1rem;
transition: all 0.3s ease;
margin-top: 0.5rem;
}

.input-container input:focus,
.input-container select:focus {
border-bottom-color: #CB2129;
}

/* État focus - rouge */
.input-container input:focus + label,
.input-container select:focus + label {
transform: translateY(-0.5rem) scale(0.85);
color: #CB2129;
font-weight: 600;
}

/* État actif (avec valeur) - vert */
.input-container.has-content label {
transform: translateY(-0.5rem) scale(0.85);
color: #10b981;
font-weight: 600;
}

/* État avec valeur - ligne verte */
.input-container.has-content input,
.input-container.has-content select {
border-bottom-color: #10b981;
}

/* État vide mais label en haut */
.input-container.active:not(.has-content) label {
transform: translateY(-0.5rem) scale(0.85);
color: #9ca3af;
font-weight: 600;
}

/* Messages d'erreur sous les champs */
.field-error {
color: #ef4444;
font-size: 0.75rem;
margin-top: 0.25rem;
display: block;
animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
from {
	opacity: 0;
	transform: translateY(-10px);
}
to {
	opacity: 1;
	transform: translateY(0);
}
}

/* État d'erreur pour les champs */
.input-container.error input,
.input-container.error select {
border-bottom-color: #ef4444;
}

.input-container.error label {
color: #ef4444;
}

/* Flatpickr - Simple White Calendar */
.flatpickr-calendar {
background: #ffffff !important;
border: 1px solid #e5e7eb !important;
border-radius: 12px !important;
box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
font-family: 'punch', system-ui, sans-serif !important;
}

/* Header simple */
.flatpickr-months {
background: #ffffff !important;
border-bottom: 1px solid #f3f4f6 !important;
}

.flatpickr-month {
background: transparent !important;
color: #1f2937 !important;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
background: #ffffff !important;
color: #1f2937 !important;
}

/* Navigation arrows */
.flatpickr-prev-month,
.flatpickr-next-month {
color: #6b7280 !important;
}

.flatpickr-prev-month:hover,
.flatpickr-next-month:hover {
color: #CB2129 !important;
}

/* Days of week */
.flatpickr-weekday {
background: #f9fafb !important;
color: #6b7280 !important;
font-weight: 500 !important;
}

/* Days */
.flatpickr-day {
color: #374151 !important;
border: none !important;
}

.flatpickr-day:hover {
background: #f3f4f6 !important;
color: #CB2129 !important;
border-color: #f3f4f6 !important;
}

.flatpickr-day.selected {
background: #CB2129 !important;
border-color: #CB2129 !important;
color: #ffffff !important;
}

.flatpickr-day.selected:hover {
background: #a01e24 !important;
border-color: #a01e24 !important;
}

.flatpickr-day.today {
background: #f3f4f6 !important;
color: #CB2129 !important;
border-color: #CB2129 !important;
}

.flatpickr-day.today:hover {
background: #CB2129 !important;
color: #ffffff !important;
}

/* Disabled days */
.flatpickr-day.flatpickr-disabled {
color: #d1d5db !important;
}

/* Calendar icon */
.calendar-icon {
position: absolute;
right: 12px;
top: 50%;
transform: translateY(-50%);
color: #9ca3af;
cursor: pointer;
font-size: 16px;
transition: color 0.2s ease;
z-index: 10;
}

.calendar-icon:hover {
color: #CB2129;
}

.input-container.has-calendar-icon input {
padding-right: 40px;
}

/* Spécifique pour les selects */
.input-container select {
cursor: pointer;
}

.input-container input::placeholder {
	color: transparent;
}

.input-container input:focus::placeholder {
	color: #9ca3af;
}

/* Forcer l'arrière-plan transparent des champs, même en autofill */
.input-container input,
.input-container select {
	background-color: transparent !important;
}

/* Chrome/Safari autofill */
input:-webkit-autofill,
select:-webkit-autofill,
input:-webkit-autofill:focus,
select:-webkit-autofill:focus {
	-webkit-box-shadow: 0 0 0px 1000px transparent inset;
	box-shadow: 0 0 0px 1000px transparent inset;
	-webkit-text-fill-color: inherit;
	transition: background-color 9999s ease-in-out 0s;
}

/* Firefox autofill */
input:-moz-autofill,
select:-moz-autofill {
	box-shadow: 0 0 0px 1000px transparent inset;
	filter: none;
}

:root {
--white: #FFFFFF;
--red: #CB2129;
--black: #000000;
--muted: #6b7280;
--card-shadow: 0 10px 30px rgba(0,0,0,0.25);
}


</style>
</head>
<body class="min-h-screen bg-gray-100 font-punch">
<!-- Header Navigation -->
<nav class="bg-transparent text-white px-6 py-4 absolute top-0 left-0 w-full z-20">
	<div class="max-w-7xl mx-auto flex items-center justify-between">
		<div class="flex items-center">
			<div class="text-punch-red font-bold text-xl">
				<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness Logo" class="w-32 h-auto logo-glow" />
			</div>
		</div>
	</div>
</nav>

<!-- Main Content -->
<div class="min-h-screen" style="background-image: url('imgs/background.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat; background-attachment: fixed;">
	<div class="max-w-7xl mx-auto p-6 pt-24">
		<div class="grid lg:grid-cols-2 gap-8">
<!-- Left Column - Form -->
<div class="bg-white/20 backdrop-blur-sm rounded-lg shadow-lg p-8 border border-white/20">
	<div class="flex items-center mb-6">
		<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness Logo" class="w-12 h-auto mr-4" />
		<h2 class="text-2xl font-bold text-gray-800">Informations Personnelles</h2>
	</div>

	<!-- Alerts section -->
	<?php if ($databaseError !== null): ?>
		<div class="mb-6">
			<div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center">
				<i class="fas fa-exclamation-triangle mr-3 text-red-500"></i>
				<span><?= e($databaseError) ?></span>
			</div>
		</div>
	<?php endif; ?>

	<?php if ($flashSuccess !== null): ?>
		<div class="mb-6">
			<div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-center">
				<i class="fas fa-check-circle mr-3 text-green-500"></i>
				<span><?= e($flashSuccess) ?></span>
			</div>
		</div>
	<?php endif; ?>

	<!-- Form section -->
	<form id="registration-form" method="post" action="<?= e($_SERVER['PHP_SELF']) ?>" autocomplete="on" novalidate>
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
			<!-- Nom -->
			<div class="input-container <?= isset($fieldErrors['name']) ? 'error' : '' ?>">
				<input
					id="name"
					name="name"
					type="text"
					placeholder=" "
					value="<?= e($prefill['name']) ?>"
					required
				/>
				<label for="name">Nom</label>
				<?php if (isset($fieldErrors['name'])): ?>
					<span class="field-error"><?= e($fieldErrors['name']) ?></span>
				<?php endif; ?>
			</div>

			<!-- Prénom -->
			<div class="input-container <?= isset($fieldErrors['firstname']) ? 'error' : '' ?>">
				<input
					id="firstname"
					name="firstname"
					type="text"
					placeholder=" "
					value="<?= e($prefill['firstname']) ?>"
					required
				/>
				<label for="firstname">Prénom</label>
				<?php if (isset($fieldErrors['firstname'])): ?>
					<span class="field-error"><?= e($fieldErrors['firstname']) ?></span>
				<?php endif; ?>
			</div>

			<!-- Email -->
			<div class="input-container <?= isset($fieldErrors['email']) ? 'error' : '' ?>">
				<input
					id="email"
					name="email"
					type="email"
					placeholder=" "
					value="<?= e($prefill['email']) ?>"
					required
				/>
				<label for="email">Email</label>
				<?php if (isset($fieldErrors['email'])): ?>
					<span class="field-error"><?= e($fieldErrors['email']) ?></span>
				<?php endif; ?>
			</div>

			<!-- Téléphone -->
			<div class="input-container <?= isset($fieldErrors['phone']) ? 'error' : '' ?>">
				<input
					id="phone"
					name="phone"
					type="tel"
					placeholder=" "
					value="<?= e($prefill['phone']) ?>"
					required
				/>
				<label for="phone">Téléphone</label>
				<?php if (isset($fieldErrors['phone'])): ?>
					<span class="field-error"><?= e($fieldErrors['phone']) ?></span>
				<?php endif; ?>
			</div>

			<!-- Date de naissance -->
			<div class="input-container <?= isset($fieldErrors['birthdate']) ? 'error' : '' ?>">
				<input
					id="birthdate"
					name="birthdate"
					type="text"
					placeholder=" "
					value="<?= e($prefill['birthdate']) ?>"
					required
				/>
				<label for="birthdate">Date de naissance</label>
				<?php if (isset($fieldErrors['birthdate'])): ?>
					<span class="field-error"><?= e($fieldErrors['birthdate']) ?></span>
				<?php endif; ?>
			</div>

			<!-- Civilité -->
			<div class="input-container <?= isset($fieldErrors['civility']) ? 'error' : '' ?>">
				<select
					id="civility"
					name="civility"
					required
				>
					<option value="">Sélectionner</option>
					<option value="M" <?= $prefill['civility'] === 'M' ? 'selected' : '' ?>>Homme</option>
					<option value="Mme" <?= $prefill['civility'] === 'Mme' ? 'selected' : '' ?>>Femme</option>
				</select>
				<label for="civility">Civilité</label>
				<?php if (isset($fieldErrors['civility'])): ?>
					<span class="field-error"><?= e($fieldErrors['civility']) ?></span>
				<?php endif; ?>
			</div>

			<!-- CIN -->
			<div class="input-container <?= isset($fieldErrors['cin']) ? 'error' : '' ?>">
				<input
					id="cin"
					name="cin"
					type="text"
					placeholder=" "
					value="<?= e($prefill['cin']) ?>"
					required
				/>
				<label for="cin">CIN</label>
				<?php if (isset($fieldErrors['cin'])): ?>
					<span class="field-error"><?= e($fieldErrors['cin']) ?></span>
				<?php endif; ?>
			</div>

			<!-- Ville -->
			<div class="input-container <?= isset($fieldErrors['city']) ? 'error' : '' ?>">
				<input
					id="city"
					name="city"
					type="text"
					placeholder=" "
					value="<?= e($prefill['city']) ?>"
					required
				/>
				<label for="city">Ville</label>
				<?php if (isset($fieldErrors['city'])): ?>
					<span class="field-error"><?= e($fieldErrors['city']) ?></span>
				<?php endif; ?>
			</div>


		</div>

		<!-- Checkboxes -->
		<div class="space-y-3 mb-8">
			<label class="flex items-start space-x-3">
				<input type="checkbox" name="accept_terms" required class="mt-1 w-4 h-4 text-punch-red border-gray-300 rounded focus:ring-punch-red">
				<span class="text-sm text-gray-700">
					J'accepte les <a href="#" class="text-punch-red underline">conditions générales</a>
				</span>
			</label>
			<label class="flex items-start space-x-3">
				<input type="checkbox" name="accept_privacy" required class="mt-1 w-4 h-4 text-punch-red border-gray-300 rounded focus:ring-punch-red">
				<span class="text-sm text-gray-700">
					J'accepte la <a href="#" class="text-punch-red underline">politique de confidentialité</a>
				</span>
			</label>
		</div>

		<!-- Bouton d'inscription -->
		<div class="text-center">
			<button
				type="submit"
				class="bg-punch-red text-white font-bold py-4 px-8 rounded-lg hover:bg-red-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
			>
				<i class="fas fa-user-plus mr-2"></i>
				S'INSCRIRE MAINTENANT
			</button>
		</div>
	</form>
</div>

<!-- Right Column - Subscription Summary -->
		</div>
	</div>
</div>





<!-- JavaScript for enhanced interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
	// Nettoyer les erreurs lors d'un refresh de page
	if (performance.navigation.type === performance.navigation.TYPE_RELOAD) {
		// Supprimer toutes les classes d'erreur
		document.querySelectorAll('.input-container.error').forEach(container => {
			container.classList.remove('error');
		});
		// Supprimer tous les messages d'erreur
		document.querySelectorAll('.field-error').forEach(error => {
			error.remove();
		});
		// Vider tous les champs
		document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"], select').forEach(field => {
			if (field.type !== 'submit' && field.type !== 'button') {
				field.value = '';
			}
		});
	}

	// Initialiser Flatpickr pour la date de naissance (calendrier simple et blanc)
	const birthdateInput = document.getElementById('birthdate');
	if (birthdateInput) {
		flatpickr(birthdateInput, {
			dateFormat: "d/m/Y",
			locale: "fr",
			maxDate: "today",
			minDate: "01/01/1940",
			allowInput: true,
			clickOpens: true,
			onChange: function(selectedDates, dateStr, instance) {
				birthdateInput.dispatchEvent(new Event('change'));
			},
			onOpen: function(selectedDates, dateStr, instance) {
				birthdateInput.closest('.input-container').classList.add('active');
			},
			onClose: function(selectedDates, dateStr, instance) {
				birthdateInput.dispatchEvent(new Event('blur'));
			}
		});

		// Ajouter une icône de calendrier simple
		const container = birthdateInput.closest('.input-container');
		container.style.position = 'relative';
		container.classList.add('has-calendar-icon');

		const calendarIcon = document.createElement('i');
		calendarIcon.className = 'fas fa-calendar-alt calendar-icon';
		container.appendChild(calendarIcon);

		// Clic sur l'icône pour ouvrir le calendrier
		calendarIcon.addEventListener('click', () => {
			birthdateInput._flatpickr.open();
		});
	}

	// Animation des labels pour les inputs
	const inputContainers = document.querySelectorAll('.input-container');

	inputContainers.forEach(container => {
		const input = container.querySelector('input, select');
		const label = container.querySelector('label');

		// Fonction pour vérifier si l'input a une valeur
		function checkValue() {
			const hasValue = input.value !== '' && input.value !== null && input.value !== undefined;

			if (hasValue) {
				container.classList.add('has-content'); // Ligne et label verts
				container.classList.add('active');
			} else {
				container.classList.remove('has-content'); // Retirer ligne verte
				// Garder active si le champ est en focus
				if (document.activeElement !== input) {
					container.classList.remove('active');
				}
			}
		}

		// Vérifier la valeur initiale
		checkValue();

		// Événements pour l'animation
		input.addEventListener('focus', function() {
			container.classList.add('active');
		});

		input.addEventListener('blur', function() {
			checkValue();
		});

		input.addEventListener('input', function() {
			checkValue();
		});

		input.addEventListener('change', function() {
			checkValue();
		});
	});

	// Add submit button loading state
	const form = document.querySelector('#registration-form');
	const submitBtns = document.querySelectorAll('button[type="submit"]');
	if (form && submitBtns.length > 0) {
		form.addEventListener('submit', function() {
			submitBtns.forEach(btn => {
				btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>INSCRIPTION EN COURS...';
				btn.disabled = true;
				btn.classList.add('opacity-75');
			});
		});
	}

	// WhatsApp button functionality
	const whatsappBtn = document.querySelector('.fixed img[alt="WhatsApp"]');
	if (whatsappBtn) {
		whatsappBtn.addEventListener('click', function() {
			window.open('https://wa.me/212709702820?text=Bonjour, j\'ai besoin d\'aide pour mon inscription', '_blank');
		});
	}
});
</script>
</body>
</html>