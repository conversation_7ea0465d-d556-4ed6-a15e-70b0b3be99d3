<!DOCTYPE html>
<html lang="fr">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<title>Contact - Power Fitness</title>
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
	<script src="https://cdn.tailwindcss.com"></script>
	<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
	<link rel="icon" type="image/png" href="imgs/LOGO POWER FITNESS.png" />
	<link rel="icon" type="image/png" sizes="32x32" href="imgs/LOGO POWER FITNESS.png" />
	<link rel="icon" type="image/png" sizes="16x16" href="imgs/LOGO POWER FITNESS.png" />
	<link rel="apple-touch-icon" href="imgs/LOGO POWER FITNESS.png" />
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						'punch-red': '#CB2129',
						'punch-white': '#FFFFFF',
						'punch-black': '#000000',
					},
					fontFamily: {
						'punch': ['Roboto', 'system-ui', '-apple-system', 'Segoe UI', 'Ubuntu', 'Cantarell', 'Noto Sans', 'Helvetica Neue', 'Arial', 'sans-serif'],
					},
					animation: {
						'fade-in': 'fadeIn 0.6s ease-out',
						'slide-up': 'slideUp 0.8s ease-out',
					}
				}
			}
		}
	</script>
	<style>
		@keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
		@keyframes slideUp { from { opacity: 0; transform: translateY(40px); } to { opacity: 1; transform: translateY(0); } }

		body {
			background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
			background-attachment: fixed;
			position: relative;
			overflow-x: hidden;
			font-family: 'punch', system-ui, sans-serif;
		}

		body::before {
			content: '';
			position: fixed; inset: 0;
			background: radial-gradient(circle at 20% 20%, rgba(203, 33, 41, 0.15) 0%, transparent 50%),
			radial-gradient(circle at 80% 80%, rgba(203, 33, 41, 0.1) 0%, transparent 50%);
			pointer-events: none; z-index: 0;
		}

		.glass { backdrop-filter: blur(10px); background: rgba(255,255,255,0.12); border: 1px solid rgba(255,255,255,0.2); }
		.section-title { color: #ffffff; font-weight: 800; font-size: 1.875rem; line-height: 2.25rem; }
		@media (min-width: 768px) { .section-title { font-size: 2.25rem; line-height: 2.5rem; } }
		.section-subtitle { color: #d1d5db; }

		.reveal { opacity: 0; transform: translateY(20px); transition: opacity .6s ease, transform .6s ease; }
		.reveal.visible { opacity: 1; transform: translateY(0); }

		/* Barre de Réseaux Sociaux */
		.social-button {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 45px;
			height: 45px;
			border-radius: 50%;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
			position: relative;
			overflow: hidden;
		}

		.whatsapp-button {
			background: #25D366;
		}

		.linkedin-button {
			background: #0077B5;
		}

		.facebook-button {
			background: #1877F2;
		}

		.tiktok-button {
			background: #000000;
		}

		.instagram-button {
			background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D, #F56040, #F77737, #FCAF45, #FFDC80);
		}

		.social-button:hover {
			transform: scale(1.1);
			box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
		}

		.social-button::before {
			content: '';
			position: absolute;
			top: 0;
			left: -100%;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
			transition: left 0.5s;
		}

		.social-button:hover::before {
			left: 100%;
		}

		/* Animation d'apparition */
		.social-button {
			animation: slideInRight 0.6s ease-out forwards;
			opacity: 0;
			transform: translateX(100px);
		}

		.social-button:nth-child(1) { animation-delay: 0.1s; }
		.social-button:nth-child(2) { animation-delay: 0.2s; }
		.social-button:nth-child(3) { animation-delay: 0.3s; }
		.social-button:nth-child(4) { animation-delay: 0.4s; }
		.social-button:nth-child(5) { animation-delay: 0.5s; }

		@keyframes slideInRight {
			to {
				opacity: 1;
				transform: translateX(0);
			}
		}

		/* Menu Mobile - Sidebar Style */
		.mobile-menu {
			position: fixed;
			top: 0;
			left: 0;
			width: 220px;
			height: 100vh;
			background: rgba(0, 0, 0, 0.95);
			backdrop-filter: blur(15px);
			z-index: 50;
			transform: translateX(-100%);
			transition: transform 0.3s ease-in-out;
			border-right: 1px solid rgba(203, 33, 41, 0.3);
		}

		.mobile-menu.active {
			transform: translateX(0);
		}

		/* Overlay pour fermer le menu */
		.mobile-menu-overlay {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100vh;
			background: rgba(0, 0, 0, 0.5);
			z-index: 49;
			opacity: 0;
			visibility: hidden;
			transition: all 0.3s ease-in-out;
		}

		.mobile-menu-overlay.active {
			opacity: 1;
			visibility: visible;
		}

		.mobile-menu-content {
			display: flex;
			flex-direction: column;
			padding: 2rem 0;
			height: 100%;
		}

		.mobile-menu-header {
			padding: 0 1.5rem 1.5rem;
			border-bottom: 1px solid rgba(255, 255, 255, 0.1);
			margin-bottom: 1rem;
		}

		.mobile-menu-header img {
			width: 120px;
			height: auto;
		}

		.mobile-menu a {
			color: white;
			font-size: 1rem;
			font-weight: 500;
			text-decoration: none;
			padding: 0.875rem 1.5rem;
			transition: all 0.3s ease;
			border-left: 3px solid transparent;
			display: flex;
			align-items: center;
		}

		.mobile-menu a:hover,
		.mobile-menu a.text-punch-red {
			background: rgba(203, 33, 41, 0.1);
			border-left-color: #cb2129;
			color: #cb2129;
		}

		.mobile-menu a.bg-punch-red {
			background: #cb2129;
			color: white;
			margin: 1rem 1.5rem;
			border-radius: 0.5rem;
			border-left: none;
			justify-content: center;
			font-weight: 600;
		}

		.mobile-menu a:nth-child(1) { transition-delay: 0.1s; }
		.mobile-menu a:nth-child(2) { transition-delay: 0.2s; }
		.mobile-menu a:nth-child(3) { transition-delay: 0.3s; }
		.mobile-menu a:nth-child(4) { transition-delay: 0.4s; }
		.mobile-menu a:nth-child(5) { transition-delay: 0.5s; }
		.mobile-menu a:nth-child(6) { transition-delay: 0.6s; }

		.mobile-menu a:hover {
			color: #cb2129;
			background: rgba(203, 33, 41, 0.1);
		}

		.hamburger {
			display: none;
			flex-direction: column;
			cursor: pointer;
			padding: 0.5rem;
		}

		.hamburger span {
			width: 25px;
			height: 3px;
			background: white;
			margin: 3px 0;
			transition: 0.3s;
			border-radius: 2px;
		}

		.hamburger.active span:nth-child(1) {
			transform: rotate(-45deg) translate(-5px, 6px);
		}

		.hamburger.active span:nth-child(2) {
			opacity: 0;
		}

		.hamburger.active span:nth-child(3) {
			transform: rotate(45deg) translate(-5px, -6px);
		}

		/* Responsive */
		@media (max-width: 768px) {
			/* Menu hamburger visible sur mobile */
			.hamburger {
				display: flex;
			}

			/* Logo plus petit sur mobile */
			nav img {
				width: 100px !important;
			}
			/* Barre de réseaux sociaux responsive */
			.social-media-bar {
				right: 8px !important;
				padding-right: 0 !important;
				top: 50% !important;
				transform: translateY(-50%) !important;
			}

			.social-media-bar .flex {
				gap: 12px !important;
			}

			.social-button {
				width: 38px;
				height: 38px;
			}
			.social-button svg {
				width: 24px;
				height: 24px;
			}
		}

		@media (max-width: 480px) {
			/* Logo encore plus petit sur très petits écrans */
			nav img {
				width: 80px !important;
			}

			/* Très petits écrans */
			.social-media-bar {
				right: 4px !important;
			}

			.social-media-bar .flex {
				gap: 8px !important;
			}

			.social-button {
				width: 32px;
				height: 32px;
			}
			.social-button svg {
				width: 20px;
				height: 20px;
			}
		}
	</style>
</head>
<body class="min-h-screen text-white" style="font-family: 'Roboto', system-ui, -apple-system, 'Segoe UI', Ubuntu, Cantarell, 'Noto Sans', 'Helvetica Neue', Arial, sans-serif;">
	<!-- Hero Background Wrapper -->
	<div class="min-h-screen" style="background-image: url('imgs/background.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat; background-attachment: fixed;">
		<!-- Navbar -->
		<nav class="bg-transparent px-6 py-4 absolute top-0 left-0 w-full z-20">
			<div class="max-w-7xl mx-auto flex items-center justify-between">
				<a href="index.html" class="flex items-center space-x-3">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" class="w-32 h-auto drop-shadow-[0_0_20px_rgba(203,33,41,0.3)]" />
				</a>

				<!-- Menu Desktop -->
				<div class="hidden md:flex items-center space-x-6">
					<a href="index.html" class="hover:text-punch-red nav-link">Accueil</a>
					<a href="abonnements.html" class="hover:text-punch-red nav-link">Abonnements</a>
					<a href="coach.html" class="hover:text-punch-red nav-link">Coachs</a>
					<a href="planning.html" class="hover:text-punch-red nav-link">Planning</a>
					<a href="contact.html" class="text-punch-red nav-link">Contact</a>
					<a href="register.php" class="px-4 py-2 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">S'inscrire</a>
				</div>

				<!-- Hamburger Menu Button -->
				<div class="hamburger md:hidden" id="hamburger">
					<span></span>
					<span></span>
					<span></span>
				</div>
			</div>
		</nav>

		<!-- Menu Mobile Overlay -->
		<div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

		<!-- Menu Mobile -->
		<div class="mobile-menu" id="mobileMenu">
			<div class="mobile-menu-content">
				<div class="mobile-menu-header">
					<img src="imgs/LOGO POWER FITNESS.png" alt="Power Fitness" />
				</div>
				<a href="index.html" class="mobile-nav-link">Accueil</a>
				<a href="abonnements.html" class="mobile-nav-link">Abonnements</a>
				<a href="coach.html" class="mobile-nav-link">Coachs</a>
				<a href="planning.html" class="mobile-nav-link">Planning</a>
				<a href="contact.html" class="mobile-nav-link text-punch-red">Contact</a>
				<a href="register.php" class="mobile-nav-link bg-punch-red">S'inscrire</a>
			</div>
		</div>

		<!-- Hero -->
		<section class="max-w-5xl mx-auto px-6 pt-56 pb-24">
			<div class="flex flex-col items-center text-center gap-8">
				<div class="animate-fade-in">
					<h1 class="text-4xl md:text-6xl font-extrabold leading-tight">Contactez <span class="text-punch-red">Power Fitness</span></h1>
					<p class="mt-5 text-gray-300 text-lg max-w-3xl mx-auto">Nous sommes là pour répondre à toutes vos questions et vous accompagner dans votre parcours fitness.</p>
				</div>
			</div>
		</section>
	</div>

	<!-- Contact Section -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<div class="grid lg:grid-cols-2 gap-12">
				<!-- Contact Info -->
				<div class="reveal">
					<h2 class="section-title">Nos coordonnées</h2>
					<p class="section-subtitle mt-2">Retrouvez-nous facilement.</p>
					
					<div class="mt-8 space-y-6">
						<div class="glass rounded-xl p-6 border border-white/20">
							<div class="flex items-center space-x-4">
								<div class="w-12 h-12 bg-punch-red rounded-lg flex items-center justify-center">
									<i class="fa-solid fa-location-dot text-white"></i>
								</div>
								<div>
									<h3 class="font-bold">Adresse</h3>
									<p class="text-gray-300">123 Rue de la Forme<br>Quartier Central<br>Votre Ville, Maroc</p>
								</div>
							</div>
						</div>

						<div class="glass rounded-xl p-6 border border-white/20">
							<div class="flex items-center space-x-4">
								<div class="w-12 h-12 bg-punch-red rounded-lg flex items-center justify-center">
									<i class="fa-solid fa-phone text-white"></i>
								</div>
								<div>
									<h3 class="font-bold">Téléphone</h3>
									<p class="text-gray-300">+212 6XX XX XX XX</p>
									<p class="text-gray-300">+212 5XX XX XX XX</p>
								</div>
							</div>
						</div>

						<div class="glass rounded-xl p-6 border border-white/20">
							<div class="flex items-center space-x-4">
								<div class="w-12 h-12 bg-punch-red rounded-lg flex items-center justify-center">
									<i class="fa-solid fa-envelope text-white"></i>
								</div>
								<div>
									<h3 class="font-bold">Email</h3>
									<p class="text-gray-300"><EMAIL></p>
									<p class="text-gray-300"><EMAIL></p>
								</div>
							</div>
						</div>

						<div class="glass rounded-xl p-6 border border-white/20">
							<div class="flex items-center space-x-4">
								<div class="w-12 h-12 bg-punch-red rounded-lg flex items-center justify-center">
									<i class="fa-solid fa-clock text-white"></i>
								</div>
								<div>
									<h3 class="font-bold">Horaires</h3>
									<p class="text-gray-300">Lundi - Samedi: 6h00 - 22h00</p>
									<p class="text-gray-300">Dimanche: 9h00 - 18h00</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Contact Form -->
				<div class="reveal">
					<h2 class="section-title">Envoyez-nous un message</h2>
					<p class="section-subtitle mt-2">Nous vous répondrons rapidement.</p>
					
					<div class="mt-8 glass rounded-xl p-8 border border-white/20">
						<form class="space-y-6">
							<div class="grid md:grid-cols-2 gap-4">
								<div>
									<label class="block text-sm font-medium text-gray-300 mb-2">Prénom</label>
									<input type="text" class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 outline-none focus:ring-2 focus:ring-punch-red text-white" placeholder="Votre prénom">
								</div>
								<div>
									<label class="block text-sm font-medium text-gray-300 mb-2">Nom</label>
									<input type="text" class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 outline-none focus:ring-2 focus:ring-punch-red text-white" placeholder="Votre nom">
								</div>
							</div>
							
							<div>
								<label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
								<input type="email" class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 outline-none focus:ring-2 focus:ring-punch-red text-white" placeholder="<EMAIL>">
							</div>
							
							<div>
								<label class="block text-sm font-medium text-gray-300 mb-2">Téléphone</label>
								<input type="tel" class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 outline-none focus:ring-2 focus:ring-punch-red text-white" placeholder="+212 6XX XX XX XX">
							</div>
							
							<div>
								<label class="block text-sm font-medium text-gray-300 mb-2">Sujet</label>
								<select class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 outline-none focus:ring-2 focus:ring-punch-red text-white">
									<option>Choisir un sujet</option>
									<option>Demande d'information</option>
									<option>Réservation cours</option>
									<option>Coaching personnalisé</option>
									<option>Autre</option>
								</select>
							</div>
							
							<div>
								<label class="block text-sm font-medium text-gray-300 mb-2">Message</label>
								<textarea rows="5" class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 outline-none focus:ring-2 focus:ring-punch-red text-white" placeholder="Votre message..."></textarea>
							</div>
							
							<button type="submit" class="w-full px-6 py-4 bg-punch-red rounded-lg font-semibold hover:bg-red-700 transition">
								<i class="fa-solid fa-paper-plane mr-2"></i>Envoyer le message
							</button>
						</form>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Map Section -->
	<section class="py-20">
		<div class="max-w-7xl mx-auto px-6">
			<h2 class="section-title text-center">Notre localisation</h2>
			<p class="section-subtitle mt-2 text-center">Venez nous rendre visite</p>
			
			<div class="mt-10 glass rounded-xl overflow-hidden border border-white/20">
				<div class="aspect-[16/9] relative">
					<iframe
						src="https://www.google.com/maps/embed?pb=!1m16!1m12!1m3!1d26616.039571596753!2d-7.653998702367655!3d33.50124843192439!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!2m1!1spower%20fitness!5e0!3m2!1sfr!2sma!4v1755861750167!5m2!1sfr!2sma"
						width="100%"
						height="100%"
						style="border:0; border-radius: 12px;"
						allowfullscreen=""
						loading="lazy"
						referrerpolicy="no-referrer-when-downgrade"
						class="absolute inset-0">
					</iframe>
				</div>
			</div>
		</div>
	</section>

	<!-- Footer -->
	<footer class="py-12 px-6" style="background-color: #000000;">
		<div class="container mx-auto max-w-6xl">
			<div class="grid md:grid-cols-4 gap-8 mb-8">
				<div class="md:col-span-1">
					<div class="flex items-center mb-4">
						<span class="text-2xl font-bold text-white">POWER</span>
						<span class="text-2xl font-bold text-brand-red">FITNESS</span>
					</div>
					<p class="text-gray-400 mb-4">Transform your limits. Unleash your power.</p>
					<div class="flex space-x-4">
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
							</svg>
						</a>
						<a href="#" class="text-gray-400 hover:text-brand-red transition-colors">
							<svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
								<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
							</svg>
						</a>
					</div>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Programs</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Strength Training</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Cardio Power</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Functional Fitness</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">HIIT Sessions</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Facility</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Equipment</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Amenities</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Hours</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Location</a></li>
					</ul>
				</div>

				<div class="md:col-span-1">
					<h4 class="text-lg font-bold text-white mb-4">Contact</h4>
					<ul class="space-y-2">
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Join Now</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Book Tour</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Support</a></li>
						<li><a href="#" class="text-gray-400 hover:text-brand-red transition-colors">Careers</a></li>
					</ul>
				</div>
			</div>

			<div class="border-t border-gray-800 pt-8 text-center">
				<p class="text-gray-400">&copy; 2025 Power Fitness. All rights reserved.</p>
			</div>
		</div>
	</footer>



	<!-- Scripts -->
	<script>
		(function() {
			// Menu Mobile Toggle
			const hamburger = document.getElementById('hamburger');
			const mobileMenu = document.getElementById('mobileMenu');
			const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
			const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

			function openMobileMenu() {
				hamburger.classList.add('active');
				mobileMenu.classList.add('active');
				mobileMenuOverlay.classList.add('active');
				document.body.style.overflow = 'hidden';
			}

			function closeMobileMenu() {
				hamburger.classList.remove('active');
				mobileMenu.classList.remove('active');
				mobileMenuOverlay.classList.remove('active');
				document.body.style.overflow = '';
			}

			hamburger.addEventListener('click', function() {
				if (mobileMenu.classList.contains('active')) {
					closeMobileMenu();
				} else {
					openMobileMenu();
				}
			});

			// Fermer le menu mobile quand on clique sur l'overlay
			mobileMenuOverlay.addEventListener('click', closeMobileMenu);

			// Fermer le menu mobile quand on clique sur un lien
			mobileNavLinks.forEach(link => {
				link.addEventListener('click', closeMobileMenu);
			});

			// Fermer le menu mobile avec la touche Escape
			document.addEventListener('keydown', function(e) {
				if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
					closeMobileMenu();
				}
			});

			// Reveal on scroll
			const revealEls = Array.from(document.querySelectorAll('.reveal'));
			const observer = new IntersectionObserver((entries) => {
				entries.forEach(entry => {
					if (entry.isIntersecting) {
						entry.target.classList.add('visible');
						observer.unobserve(entry.target);
					}
				});
			}, { threshold: 0.15 });
			revealEls.forEach(el => observer.observe(el));
		})();
	</script>

	<!-- Barre de Réseaux Sociaux Flottante -->
	 <div class="social-media-bar fixed right-4 top-1/2 transform -translate-y-1/2 z-50" style="padding-right: 10px;">
		<div class="flex flex-col space-y-5">
			<!-- WhatsApp -->
			<a href="https://api.whatsapp.com/send?phone=+212709702820" target="_blank" class="whatsapp-button social-button" onclick="this.href=window.innerWidth > 768 ? 'https://web.whatsapp.com/send?phone=+212709702820' : 'https://api.whatsapp.com/send?phone=+212709702820';">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.890-5.335 11.893-11.893A11.821 11.821 0 0020.525 3.687"></path>
				</svg>
			</a>

			<!-- LinkedIn -->
			<a class="linkedin-button social-button" href="https://www.linkedin.com/company/powerfitness/" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path>
				</svg>
			</a>

			<!-- Facebook -->
			<a class="facebook-button social-button" href="https://www.facebook.com/powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"></path>
				</svg>
			</a>

			<!-- TikTok -->
			<a class="tiktok-button social-button" href="https://www.tiktok.com/@powerfitness" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"></path>
				</svg>
			</a>

			<!-- Instagram -->
			<a class="instagram-button social-button" href="https://www.instagram.com/powerfitness/" target="_blank">
				<svg width="30" height="30" viewBox="0 0 24 24" fill="white">
					<path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path>
				</svg>
			</a>
		</div>
	</div>

</body>
</html>
